# Excel导入功能使用指南

## 概述

本文档介绍了gisimportservice项目中Excel导入功能的使用方法、配置说明和API接口。

## 功能特性

### ✅ 已实现功能

1. **Excel文件解析**
   - 支持.xlsx和.xls格式
   - 多工作表支持
   - 自动识别表头和数据行

2. **模板驱动导入**
   - 基于GisManageTemplate配置
   - 灵活的字段映射
   - 支持数据类型转换

3. **批量处理**
   - 大文件分批处理
   - 可配置批次大小
   - 内存优化处理

4. **数据验证**
   - 字段类型验证
   - 坐标范围验证
   - 数据完整性检查

5. **坐标转换**
   - 多坐标系支持
   - 自动坐标转换
   - 几何数据处理

6. **任务管理**
   - 导入任务跟踪
   - 进度监控
   - 错误报告

## 配置说明

### 1. 应用配置

在`application-dev.yml`中配置Excel导入相关参数：

```yaml
gis:
  import:
    excel:
      # Excel导入功能开关
      enabled: true
      # 默认批次大小
      default-batch-size: 1000
      # 最大文件大小（MB）
      max-file-size: 100
      # 支持的文件格式
      supported-formats: [".xlsx", ".xls"]
      # 临时文件存储路径
      temp-path: ${gis.import.task.upload.temp-path}/excel
      # 数据处理配置
      processing:
        # 是否启用异步处理
        async-enabled: true
        # 线程池大小
        thread-pool-size: 4
        # 队列大小
        queue-size: 1000
      # Excel特定验证配置
      validation:
        # 检查表头格式
        check-header-format: true
        # 检查数据一致性
        check-data-consistency: true
        # Excel最大错误率
        max-error-rate: 5
        # 是否严格模式
        strict-mode: false
```

### 2. 模板配置

创建Excel导入模板时，需要配置以下字段：

```json
{
  "id": 1,
  "nameZh": "Excel导入模板",
  "templateType": "excel",
  "tableName": "target_table",
  "datasourceName": "target_datasource",
  "dataBase": "target_database",
  "thLine": 2,
  "sheetName": "Sheet1",
  "originalCoordinateSystem": "CGCS2000",
  "targetCoordinateSystem": "WenZhou2000",
  "isZh": true,
  "map": [
    {
      "position": 0,
      "fieldName": "name",
      "fieldType": "string",
      "required": true
    },
    {
      "position": 1,
      "fieldName": "x_coord",
      "fieldType": "double",
      "required": true
    },
    {
      "position": 2,
      "fieldName": "y_coord",
      "fieldType": "double",
      "required": true
    }
  ],
  "excelConfig": {
    "batchSize": 1000,
    "dataStartRow": 2,
    "headerRow": 1
  }
}
```

## API接口

### 1. Excel文件导入

**接口地址：** `POST /api/excel-import/import`

**请求参数：**
- `file` (MultipartFile): Excel文件
- `templateId` (Integer): 模板ID
- `target` (String, 默认"import"): 导入目标类型
- `createdBy` (String, 可选): 创建用户

**注意：** 工作表名称现在从模板配置中的`sheetName`字段获取，无需手动指定。

**响应示例：**
```json
{
  "success": true,
  "message": "导入成功",
  "data": {
    "success": true,
    "message": "导入成功",
    "taskId": 123,
    "totalRecords": 1000,
    "successRecords": 995,
    "errorRecords": 5,
    "processingTimeMs": 15000,
    "fileInfo": {
      "fileName": "data.xlsx",
      "fileSize": 2048576,
      "sheetName": "Sheet1",
      "fileType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    },
    "templateInfo": {
      "templateId": 1,
      "templateName": "Excel导入模板",
      "tableName": "target_table",
      "datasourceName": "target_datasource",
      "coordinateTransform": "CGCS2000 -> WenZhou2000"
    }
  }
}
```

### 2. 分析Excel文件结构

**接口地址：** `POST /api/excel-import/analyze`

**请求参数：**
- `file` (MultipartFile): Excel文件
- `headerRow` (Integer, 默认1): 表头行号

**响应示例：**
```json
{
  "success": true,
  "message": "Excel文件分析成功",
  "data": {
    "totalSheets": 1,
    "fileName": "data.xlsx",
    "fileSize": 2048576,
    "sheets": [
      {
        "name": "Sheet1",
        "totalRows": 1001,
        "totalColumns": 5,
        "headers": ["名称", "X坐标", "Y坐标", "类型", "备注"],
        "preview": [
          ["名称", "X坐标", "Y坐标", "类型", "备注"],
          ["点位1", "120.123", "28.456", "监测点", "测试数据"],
          ["点位2", "120.124", "28.457", "监测点", "测试数据"]
        ]
      }
    ]
  }
}
```

### 3. 获取工作表名称列表

**接口地址：** `POST /api/excel-import/sheets`

**请求参数：**
- `file` (MultipartFile): Excel文件

**响应示例：**
```json
{
  "success": true,
  "message": "获取工作表名称成功",
  "data": ["Sheet1", "Sheet2", "数据表"]
}
```

### 4. 验证Excel数据

**接口地址：** `POST /api/excel-import/validate`

**请求参数：**
- `file` (MultipartFile): Excel文件
- `templateId` (Integer): 模板ID
- `sheetName` (String, 可选): 工作表名称

**响应示例：**
```json
{
  "success": true,
  "message": "Excel数据验证完成",
  "data": {
    "valid": true,
    "totalRecords": 1000,
    "errorRecords": 5,
    "errorRate": 0.5,
    "errors": [
      {
        "errorType": "TYPE_MISMATCH",
        "message": "数据类型不匹配",
        "rowNumber": 10,
        "columnName": "X坐标",
        "errorValue": "abc",
        "expectedValue": "数字",
        "suggestion": "请输入有效的数字",
        "level": "ERROR"
      }
    ],
    "warnings": []
  }
}
```

### 5. 批量导入Excel数据

**接口地址：** `POST /api/excel-import/batch-import`

**请求参数：**
- `file` (MultipartFile): Excel文件
- `templateId` (Integer): 模板ID
- `batchSize` (Integer, 可选): 批次大小
- `target` (String, 默认"import"): 导入目标类型
- `createdBy` (String, 可选): 创建用户

### 6. 获取配置信息

**接口地址：** `GET /api/excel-import/config`

**响应示例：**
```json
{
  "success": true,
  "message": "获取配置信息成功",
  "data": {
    "enabled": true,
    "maxFileSize": 100,
    "maxFileSizeBytes": 104857600,
    "supportedFormats": [".xlsx", ".xls"],
    "defaultBatchSize": 1000,
    "processing": {
      "asyncEnabled": true,
      "threadPoolSize": 4,
      "queueSize": 1000
    },
    "validation": {
      "checkHeaderFormat": true,
      "checkDataConsistency": true,
      "maxErrorRate": 5.0,
      "strictMode": false
    }
  }
}
```

### 7. 健康检查

**接口地址：** `GET /api/excel-import/health`

**响应示例：**
```json
{
  "success": true,
  "message": "Excel导入服务运行正常",
  "timestamp": 1690531200000,
  "enabled": true
}
```

## 使用示例

### 1. 基本导入流程

```javascript
// 1. 上传并分析Excel文件
const analyzeResponse = await fetch('/api/excel-import/analyze', {
  method: 'POST',
  body: formData
});

// 2. 验证数据
const validateResponse = await fetch('/api/excel-import/validate', {
  method: 'POST',
  body: formData
});

// 3. 执行导入
const importResponse = await fetch('/api/excel-import/import', {
  method: 'POST',
  body: formData
});
```

### 2. 批量导入大文件

```javascript
const formData = new FormData();
formData.append('file', file);
formData.append('templateId', '1');
formData.append('batchSize', '500');
formData.append('target', 'import');
formData.append('createdBy', 'user123');

const response = await fetch('/api/excel-import/batch-import', {
  method: 'POST',
  body: formData
});
```

## 错误处理

### 常见错误码

| 错误类型 | 错误信息 | 解决方案 |
|---------|---------|---------|
| 文件格式错误 | 不支持的文件格式 | 使用.xlsx或.xls格式 |
| 文件大小超限 | 文件大小超过限制 | 减小文件大小或调整配置 |
| 模板不存在 | 模板不存在 | 检查模板ID是否正确 |
| 数据类型错误 | 数据类型不匹配 | 检查Excel数据格式 |
| 坐标范围错误 | 坐标值超出范围 | 检查坐标数据的有效性 |

### 错误响应格式

```json
{
  "success": false,
  "message": "具体错误信息",
  "data": null
}
```

## 性能优化建议

1. **文件大小控制**
   - 建议单个文件不超过50MB
   - 大文件建议分批上传

2. **批次大小调优**
   - 默认1000条记录一批
   - 可根据服务器性能调整

3. **内存管理**
   - 使用流式处理避免内存溢出
   - 及时释放资源

4. **并发控制**
   - 避免同时导入多个大文件
   - 合理配置线程池大小

## 故障排除

### 1. 导入失败

**可能原因：**
- 文件格式不正确
- 模板配置错误
- 数据库连接问题
- 内存不足

**解决方案：**
- 检查文件格式和内容
- 验证模板配置
- 检查数据库连接
- 增加JVM内存

### 2. 坐标转换失败

**可能原因：**
- 坐标系配置错误
- 坐标数据格式错误
- 转换服务不可用

**解决方案：**
- 检查坐标系配置
- 验证坐标数据格式
- 确认转换服务状态

### 3. 性能问题

**可能原因：**
- 批次大小不合适
- 数据库性能瓶颈
- 内存不足

**解决方案：**
- 调整批次大小
- 优化数据库配置
- 增加服务器资源

---

## 总结

Excel导入功能已成功集成到gisimportservice项目中，提供了完整的文件导入、数据验证、坐标转换和任务管理功能。通过合理的配置和使用，可以高效地处理各种Excel数据导入需求。
