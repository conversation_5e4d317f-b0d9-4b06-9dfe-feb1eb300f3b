# Excel Config 列缺失问题解决方案

## 问题描述

在调用模板查询接口时出现以下错误：
```
org.postgresql.util.PSQLException: ERROR: column "excel_config" does not exist
```

## 问题原因

1. `GisManageTemplate` 实体类中定义了 `excelConfigJson` 字段，映射到数据库的 `excel_config` 列
2. 但实际的数据库表 `gis_manage_template` 中不存在 `excel_config` 列
3. MyBatis-Plus 的 `selectById` 方法自动生成包含所有实体字段的 SQL 查询

## 解决方案

### 方案一：添加数据库列（推荐）

#### 1. 执行数据库迁移脚本

```sql
-- 为 gis_manage_template 表添加 excel_config 列
ALTER TABLE gis_manage_template 
ADD COLUMN excel_config TEXT;

-- 添加列注释
COMMENT ON COLUMN gis_manage_template.excel_config IS 'Excel配置信息，JSON格式存储';

-- 为现有Excel模板设置默认配置
UPDATE gis_manage_template 
SET excel_config = '{"sheetName":"Sheet1","headerRow":1,"dataStartRow":2,"batchSize":1000}'
WHERE template_type = 'excel' AND excel_config IS NULL;
```

#### 2. 恢复实体类映射

执行完数据库迁移后，需要恢复 `GisManageTemplate.java` 中的字段映射：

```java
/**
 * Excel配置（JSON格式存储）
 */
@TableField("excel_config")
private String excelConfigJson;
```

### 方案二：临时修复（已实施）

已临时修改 `GisManageTemplate.java`，将 `excelConfigJson` 字段标记为非数据库字段：

```java
/**
 * Excel配置（JSON格式存储）
 * 临时注释数据库映射，等待数据库添加 excel_config 列后恢复
 */
@TableField(exist = false)
// @TableField("excel_config")  // 临时注释，等待数据库列创建
private String excelConfigJson;
```

## 验证步骤

### 1. 验证临时修复是否生效

重启应用后，尝试调用模板查询接口：
```
GET /api/template/{templateId}
```

### 2. 验证数据库列添加

执行以下 SQL 验证列是否成功添加：
```sql
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'gis_manage_template' 
AND column_name = 'excel_config';
```

## 后续步骤

1. **立即执行**：临时修复已完成，应用可以正常运行
2. **计划执行**：在合适的维护窗口执行数据库迁移脚本
3. **完成修复**：数据库列添加完成后，恢复实体类的正常映射

## 影响评估

### 临时修复的影响
- ✅ 解决当前查询错误
- ✅ 不影响现有 Shapefile 导入功能
- ⚠️ Excel 配置信息暂时无法持久化到数据库
- ⚠️ 需要在代码中处理 `excelConfigJson` 为空的情况

### 完整修复后的效果
- ✅ 完全支持 Excel 配置的数据库存储
- ✅ 支持复杂的 Excel 导入配置
- ✅ 与现有模板系统完全集成

## 相关文件

- `src/main/java/com/zjxy/gisimportservice/entity/GisManageTemplate.java` - 实体类
- `database/migration/add_excel_config_column.sql` - 数据库迁移脚本
- `docs/excel_config_column_fix.md` - 本文档
