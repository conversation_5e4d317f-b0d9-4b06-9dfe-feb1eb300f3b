# Excel导入调试指南

## 问题分析

### 当前问题
Excel导入显示总记录数为0，需要调试数据读取流程。

### 模板配置分析
- **模板ID**: 874
- **thLine**: 1 (原配置表示数据开始行)
- **sheetName**: "燃气管线"
- **templateType**: "excel"
- **字段映射**: 13个已选中的字段映射

### Excel文件结构
- 第1行：表头（"管线编号", "原管线编码", "关键字"等）
- 第2行开始：实际数据

## 修复内容

### 1. 修复 getExcelDataStartRow() 方法
**问题**: 原方法直接返回thLine值，与EasyExcel语义不匹配
**修复**: 修正为返回表头行数（headRowNumber）

```java
public Integer getExcelDataStartRow() {
    // 对于当前燃气管线模板：第1行是表头，第2行开始是数据
    // 所以headRowNumber应该是1
    if (this.thLine != null && this.thLine == 1) {
        return 1; // 表示第1行是表头
    }
    return 1; // 默认：第1行是表头
}
```

### 2. 增强 ExcelDataListener 调试
- 添加详细的数据处理日志
- 检查空数据行的处理
- 验证字段映射逻辑

### 3. 增强 ExcelImportServiceImpl 调试
- 记录EasyExcel配置参数
- 输出读取完成后的统计信息

## 测试步骤

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载修复后的代码
```

### 2. 测试Excel导入
```bash
# 使用Postman或curl测试导入接口
POST /api/excel-import/import
- file: 燃气管线Excel文件
- templateId: 874
- sheetName: 燃气管线
- target: import
```

### 3. 查看日志
关注以下关键日志：
```
Excel读取配置 - 工作表: 燃气管线, 表头行数: 1, 模板thLine: 1
处理第 X 行数据: {0=5300, 1=T84, 2=T85, ...}
映射字段: gxbh (位置0) = 5300
第 X 行数据转换完成，属性数量: 13
Excel读取完成 - 监听器统计: 总记录=X, 成功=X, 错误=0
```

### 4. 验证结果
期望结果：
- 总记录数 > 0
- 成功记录数 > 0
- 错误记录数 = 0

## 可能的问题和解决方案

### 问题1: 工作表名称不匹配
**症状**: 仍然显示总记录数为0
**解决**: 检查Excel文件中的实际工作表名称是否为"燃气管线"

### 问题2: 字段映射配置问题
**症状**: 数据读取到但转换失败
**解决**: 检查模板的map配置中checked=true的字段

### 问题3: 数据类型转换错误
**症状**: 有错误记录
**解决**: 检查convertValue方法的数据类型转换逻辑

## 调试命令

### 启用调试日志
在application-dev.yml中添加：
```yaml
logging:
  level:
    com.zjxy.gisimportservice.listener.ExcelDataListener: DEBUG
    com.zjxy.gisimportservice.service.Impl.ExcelImportServiceImpl: DEBUG
```

### 查看实时日志
```bash
tail -f logs/application.log | grep -E "(Excel读取|处理第|映射字段|数据转换)"
```

## 预期修复效果

修复后，Excel导入应该能够：
1. 正确识别表头行（第1行）
2. 从第2行开始读取数据
3. 正确映射13个已选中的字段
4. 显示正确的总记录数和成功记录数

## 后续优化建议

1. **配置优化**: 考虑在模板中明确区分表头行号和数据开始行号
2. **错误处理**: 增强数据类型转换的错误处理
3. **性能优化**: 对于大文件，考虑异步处理和进度反馈
4. **用户体验**: 提供更详细的导入结果反馈
