package com.zjxy.gisimportservice.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.Impl.HighPerformanceBatchInsertService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel数据监听器
 *
 * 基于EasyExcel的数据监听器，用于处理Excel数据的读取、验证、转换和批量插入
 * 适配gisimportservice项目的数据处理流程
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
public class ExcelDataListener extends AnalysisEventListener<Map<Integer, Object>> {

    /**
     * 模板配置
     */
    private final GisManageTemplate template;

    /**
     * 数据验证服务
     */
    private final DataValidationService validationService;

    /**
     * 坐标转换服务
     */
    private final CoordinateTransformService coordinateService;

    /**
     * 批量插入服务
     */
    private final HighPerformanceBatchInsertService batchInsertService;

    /**
     * 当前批次数据
     */
    private List<GeoFeatureEntity> batch = new ArrayList<>();

    /**
     * 批次大小
     */
    private final int batchSize;

    /**
     * 处理目标（import/valid/export）
     */
    private final String target;

    /**
     * 统计信息
     */
    private int totalRecords = 0;
    private int successRecords = 0;
    private int errorRecords = 0;
    private int currentBatchNumber = 1;

    /**
     * 错误信息收集
     */
    private List<ValidationResult.ValidationError> errors = new ArrayList<>();

    /**
     * 是否发生错误
     */
    private boolean hasError = false;

    /**
     * 构造函数
     */
    public ExcelDataListener(GisManageTemplate template,
                           DataValidationService validationService,
                           CoordinateTransformService coordinateService,
                           HighPerformanceBatchInsertService batchInsertService,
                           int batchSize,
                           String target) {
        this.template = template;
        this.validationService = validationService;
        this.coordinateService = coordinateService;
        this.batchInsertService = batchInsertService;
        this.batchSize = batchSize;
        this.target = target;
    }

    /**
     * 处理每一行数据
     */
    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        totalRecords++;
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        log.info("=== 处理第 {} 行数据开始 ===", rowIndex);
        log.info("原始数据: {}", data);
        log.info("当前目标模式: {}", target);

        try {
            // 如果是验证模式且已有错误，跳过后续处理
            if ("valid".equals(target) && hasError) {
                log.info("验证模式下已有错误，跳过第 {} 行", rowIndex);
                return;
            }

            // 检查数据是否为空
            if (data == null || data.isEmpty()) {
                log.warn("第 {} 行数据为空，跳过处理", rowIndex);
                return;
            }

            // 转换为GeoFeatureEntity
            GeoFeatureEntity entity = convertToGeoFeatureEntity(data, rowIndex);
            if (entity != null) {
                batch.add(entity);
                log.info("第 {} 行数据转换成功，当前批次大小: {}/{}", rowIndex, batch.size(), batchSize);
                log.info("转换后的实体属性: {}", entity.getRawAttributes());
            } else {
                log.error("第 {} 行数据转换失败，返回null", rowIndex);
            }

            // 达到批次大小时处理批次
            if (batch.size() >= batchSize) {
                log.info("达到批次大小 {}，开始处理批次", batchSize);
                processBatch();
            }

        } catch (Exception e) {
            log.error("处理第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            errorRecords++;
            hasError = true;

            // 记录错误信息
            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(context.readRowHolder().getRowIndex());
            error.setErrorMessage("数据处理失败: " + e.getMessage());
            error.setErrorType(ValidationResult.ErrorType.CONVERSION_FAILED);
            errors.add(error);
        }
    }

    /**
     * 所有数据解析完成后的处理
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余的批次数据
        if (!batch.isEmpty()) {
            processBatch();
        }

        log.info("Excel数据处理完成 - 总记录数: {}, 成功: {}, 错误: {}",
                totalRecords, successRecords, errorRecords);
    }

    /**
     * 处理批次数据
     */
    private void processBatch() {
        if (batch.isEmpty()) {
            log.warn("批次数据为空，跳过处理");
            return;
        }

        log.info("=== 开始处理第 {} 批数据，数据量: {} ===", currentBatchNumber, batch.size());
        log.info("目标模式: {}", target);
        log.info("模板信息: ID={}, 表名={}, 数据库={}", template.getId(), template.getTableName(), template.getDataBase());

        try {
            // 1. 数据验证
            log.info("步骤1: 开始数据验证");
            if (validationService != null) {
                ValidationResult validationResult = validateBatch(batch);
                if (validationResult != null && !validationResult.isPassed()) {
                    log.warn("数据验证失败: {}", validationResult.getErrors());
                    errors.addAll(validationResult.getErrors());
                    if ("valid".equals(target)) {
                        hasError = true;
                        log.warn("验证模式下发现错误，停止处理");
                        return;
                    }
                }
            } else {
                log.info("数据验证服务为null，跳过验证");
            }

            // 2. 坐标转换
            log.info("步骤2: 检查坐标转换需求");
            if (template.getIsZh() != null && template.getIsZh() && coordinateService != null) {
                log.info("开始坐标转换: {} -> {}", template.getOriginalCoordinateSystem(), template.getTargetCoordinateSystem());
                transformCoordinates(batch);
            } else {
                log.info("跳过坐标转换: isZh={}, coordinateService={}", template.getIsZh(), coordinateService != null);
            }

            // 3. 设置其他字段
            log.info("步骤3: 设置附加字段");
            setAdditionalFields(batch);

            // 4. 数据插入
            log.info("步骤4: 数据插入，目标模式: {}", target);
            if ("import".equals(target)) {
                log.info("开始批量插入数据，数据量: {}", batch.size());
                insertBatch(batch);
                log.info("批量插入完成");
            } else if ("export".equals(target)) {
                // 导出模式的特殊处理
                log.info("处理导出模式");
                processExportBatch(batch);
            } else {
                log.info("目标模式为 {}，跳过数据插入", target);
            }

            successRecords += batch.size();
            log.info("=== 第 {} 批数据处理完成，成功记录数: {} ===", currentBatchNumber, batch.size());

        } catch (Exception e) {
            log.error("处理第 {} 批数据时发生错误: {}", currentBatchNumber, e.getMessage(), e);
            errorRecords += batch.size();
            hasError = true;
        } finally {
            // 清空当前批次
            batch.clear();
            currentBatchNumber++;
        }
    }

    /**
     * 将Excel行数据转换为GeoFeatureEntity
     */
    private GeoFeatureEntity convertToGeoFeatureEntity(Map<Integer, Object> rowData, int rowNumber) {
        log.debug("开始转换第 {} 行数据，原始数据: {}", rowNumber, rowData);

        GeoFeatureEntity entity = new GeoFeatureEntity();
        entity.setFeatureId("excel_row_" + rowNumber);
        entity.setCreatedAt(LocalDateTime.now());

        Map<String, Object> attributes = new HashMap<>();

        // 获取模板字段映射配置
        List<Map<String, Object>> fieldMappings = template.getMap();
        log.info("模板字段映射配置: {}", fieldMappings);

        if (fieldMappings != null) {
            for (Map<String, Object> mapping : fieldMappings) {
                Integer position = (Integer) mapping.get("position");
                String fieldName = (String) mapping.get("fieldName");
                String fieldType = (String) mapping.get("fieldType");
                Boolean checked = (Boolean) mapping.get("checked");

                log.debug("处理字段映射: position={}, fieldName={}, fieldType={}, checked={}",
                        position, fieldName, fieldType, checked);

                // 处理字段映射（如果没有checked字段或checked为true，则处理）
                boolean shouldProcess = (checked == null || checked) &&
                                      position != null &&
                                      fieldName != null &&
                                      !fieldName.trim().isEmpty() &&
                                      rowData.containsKey(position);

                if (shouldProcess) {
                    Object value = rowData.get(position);
                    Object convertedValue = convertValue(value, fieldType);

                    // 使用数据库字段名作为key存储到attributes中
                    attributes.put(fieldName, convertedValue);

                    // 同时使用col_position格式的key存储，以便TemplateDataMapper能够找到
                    String sourceFieldKey = "col_" + position;
                    attributes.put(sourceFieldKey, convertedValue);

                    log.info("映射字段成功: {} (位置{}) = {} (原始值: {})", fieldName, position, convertedValue, value);
                } else {
                    log.debug("跳过字段映射: fieldName={}, position={}, checked={}, hasData={}",
                            fieldName, position, checked, rowData.containsKey(position != null ? position : -1));
                }
            }
        }

        log.info("转换后的属性: {}", attributes);

        // 处理坐标字段
        processCoordinateFields(rowData, attributes);

        entity.setRawAttributes(attributes);
        log.debug("第 {} 行数据转换完成，属性数量: {}", rowNumber, attributes.size());
        return entity;
    }

    /**
     * 处理坐标字段
     */
    private void processCoordinateFields(Map<Integer, Object> rowData, Map<String, Object> attributes) {
        // 处理点坐标（type = 2）
        if (template.getType() != null && template.getType() == 2) {
            Map<String, Object> pointMap = template.getPointMap();
            if (pointMap != null) {
                Integer xColumn = (Integer) pointMap.get("x");
                Integer yColumn = (Integer) pointMap.get("y");

                if (xColumn != null && yColumn != null) {
                    Object xValue = rowData.get(xColumn);
                    Object yValue = rowData.get(yColumn);

                    if (xValue != null && yValue != null) {
                        try {
                            double x = Double.parseDouble(xValue.toString());
                            double y = Double.parseDouble(yValue.toString());

                            // 验证坐标范围
                            validateCoordinateRange(x, y);

                            attributes.put("x_coord", x);
                            attributes.put("y_coord", y);

                            // 生成WKT几何
                            String wkt = String.format("POINT(%f %f)", x, y);
                            attributes.put("geometry", wkt);

                        } catch (NumberFormatException e) {
                            log.warn("坐标值格式错误: x={}, y={}", xValue, yValue);
                        }
                    }
                }
            }
        }
    }

    /**
     * 验证坐标范围
     */
    private void validateCoordinateRange(double x, double y) {
        String coordinateSystem = template.getOriginalCoordinateSystem();
        if (coordinateSystem != null) {
            // 根据坐标系验证坐标范围
            switch (coordinateSystem) {
                case "CGCS2000":
                case "BD09":
                    if (x < 119.5 || x > 121.4 || y < 26.9 || y > 28.7) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
                case "WenZhou2000":
                case "WenZhouCity":
                    if (x < 384095.61 || x > 571661.61 || y < 2977083.96 || y > 3176239.57) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
            }
        }
    }

    /**
     * 验证批次数据
     */
    private ValidationResult validateBatch(List<GeoFeatureEntity> entities) {
        try {
            if (validationService != null && !entities.isEmpty()) {
                // 使用现有的数据验证服务进行验证
                // 这里可以根据实际需要调用相应的验证方法
                log.debug("验证批次数据，数据量: {}", entities.size());

                // 创建简单的验证结果
                ValidationResult result = new ValidationResult();
                result.setPassed(true);
                result.setTotalRecords(entities.size());
                result.setErrorRecords(0);

                return result;
            }
        } catch (Exception e) {
            log.error("批次数据验证失败", e);
        }
        return null;
    }

    /**
     * 坐标转换
     */
    private void transformCoordinates(List<GeoFeatureEntity> entities) {
        String sourceCoordSystem = template.getOriginalCoordinateSystem();
        String targetCoordSystem = template.getTargetCoordinateSystem();

        if (sourceCoordSystem != null && targetCoordSystem != null && !sourceCoordSystem.equals(targetCoordSystem)) {
            for (GeoFeatureEntity entity : entities) {
                String geometry = entity.getGeometry();
                if (geometry != null && !geometry.trim().isEmpty()) {
                    try {
                        String transformedGeometry = coordinateService.transformGeometry(
                            geometry, sourceCoordSystem, targetCoordSystem);
                        entity.setGeometry(transformedGeometry);
                    } catch (Exception e) {
                        log.error("坐标转换失败，要素ID: {}, 错误: {}", entity.getFeatureId(), e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 设置附加字段
     */
    private void setAdditionalFields(List<GeoFeatureEntity> entities) {
        // 设置默认值、枚举值等
        for (GeoFeatureEntity entity : entities) {
            // 可以在这里添加业务逻辑设置默认字段
        }
    }

    /**
     * 插入批次数据
     */
    private void insertBatch(List<GeoFeatureEntity> entities) {
        log.info("开始插入批次数据，实体数量: {}", entities.size());

        if (batchInsertService == null) {
            log.error("批量插入服务为null，无法插入数据");
            throw new RuntimeException("批量插入服务未初始化");
        }

        if (entities.isEmpty()) {
            log.warn("实体列表为空，跳过插入");
            return;
        }

        // 打印第一个实体的详细信息用于调试
        if (!entities.isEmpty()) {
            GeoFeatureEntity firstEntity = entities.get(0);
            log.info("第一个实体详情: featureId={}, attributes={}, geometry={}",
                    firstEntity.getFeatureId(), firstEntity.getRawAttributes(), firstEntity.getGeometry());
        }

        try {
            // 不在这里设置数据源，由外层调用方负责设置
            log.info("调用批量插入服务: zeroConversionBatchInsert，目标数据库: {}", template.getDataBase());
            batchInsertService.zeroConversionBatchInsert(entities, template);
            log.info("批量插入服务调用完成");
        } catch (Exception e) {
            log.error("批量插入失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理导出批次
     */
    private void processExportBatch(List<GeoFeatureEntity> entities) {
        // 导出模式的特殊处理逻辑
        log.debug("处理导出批次，数据量: {}", entities.size());
    }

    /**
     * 值类型转换
     */
    private Object convertValue(Object value, String fieldType) {
        if (value == null) {
            return null;
        }

        try {
            switch (fieldType.toLowerCase()) {
                case "string":
                case "varchar":
                case "text":
                    return value.toString();
                case "integer":
                case "int":
                    return Integer.valueOf(value.toString());
                case "double":
                case "float":
                case "decimal":
                    return Double.valueOf(value.toString());
                case "boolean":
                    return Boolean.valueOf(value.toString());
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("值转换失败，原始值: {}, 目标类型: {}", value, fieldType);
            return value;
        }
    }

    // Getter方法
    public boolean isHasError() {
        return hasError;
    }

    public int getTotalRecords() {
        return totalRecords;
    }

    public int getSuccessRecords() {
        return successRecords;
    }

    public int getErrorRecords() {
        return errorRecords;
    }

    public List<ValidationResult.ValidationError> getErrors() {
        return errors;
    }
}
