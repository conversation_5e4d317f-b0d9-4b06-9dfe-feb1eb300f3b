package com.zjxy.gisimportservice.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.Impl.HighPerformanceBatchInsertService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel数据监听器
 *
 * 基于EasyExcel的数据监听器，用于处理Excel数据的读取、验证、转换和批量插入
 * 适配gisimportservice项目的数据处理流程
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
public class ExcelDataListener extends AnalysisEventListener<Map<Integer, Object>> {

    /**
     * 模板配置
     */
    private final GisManageTemplate template;

    /**
     * 数据验证服务
     */
    private final DataValidationService validationService;

    /**
     * 坐标转换服务
     */
    private final CoordinateTransformService coordinateService;

    /**
     * 批量插入服务
     */
    private final HighPerformanceBatchInsertService batchInsertService;

    /**
     * 当前批次数据
     */
    private List<GeoFeatureEntity> batch = new ArrayList<>();

    /**
     * 批次大小
     */
    private final int batchSize;

    /**
     * 处理目标（import/valid/export）
     */
    private final String target;

    /**
     * 统计信息
     */
    private int totalRecords = 0;
    private int successRecords = 0;
    private int errorRecords = 0;
    private int currentBatchNumber = 1;

    /**
     * 错误信息收集
     */
    private List<ValidationResult.ValidationError> errors = new ArrayList<>();

    /**
     * 是否发生错误
     */
    private boolean hasError = false;

    /**
     * 构造函数
     */
    public ExcelDataListener(GisManageTemplate template,
                           DataValidationService validationService,
                           CoordinateTransformService coordinateService,
                           HighPerformanceBatchInsertService batchInsertService,
                           int batchSize,
                           String target) {
        this.template = template;
        this.validationService = validationService;
        this.coordinateService = coordinateService;
        this.batchInsertService = batchInsertService;
        this.batchSize = batchSize;
        this.target = target;
    }

    /**
     * 处理每一行数据
     */
    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        totalRecords++;

        try {
            // 如果是验证模式且已有错误，跳过后续处理
            if ("valid".equals(target) && hasError) {
                return;
            }

            // 转换为GeoFeatureEntity
            GeoFeatureEntity entity = convertToGeoFeatureEntity(data, context.readRowHolder().getRowIndex() + 1);
            if (entity != null) {
                batch.add(entity);
            }

            // 达到批次大小时处理批次
            if (batch.size() >= batchSize) {
                processBatch();
            }

        } catch (Exception e) {
            log.error("处理第 {} 行数据时发生错误: {}", context.readRowHolder().getRowIndex() + 1, e.getMessage());
            errorRecords++;
            hasError = true;

            // 记录错误信息
            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(context.readRowHolder().getRowIndex());
            error.setErrorMessage("数据处理失败: " + e.getMessage());
            error.setErrorType(ValidationResult.ErrorType.CONVERSION_FAILED);
            errors.add(error);
        }
    }

    /**
     * 所有数据解析完成后的处理
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余的批次数据
        if (!batch.isEmpty()) {
            processBatch();
        }

        log.info("Excel数据处理完成 - 总记录数: {}, 成功: {}, 错误: {}",
                totalRecords, successRecords, errorRecords);
    }

    /**
     * 处理批次数据
     */
    private void processBatch() {
        if (batch.isEmpty()) {
            return;
        }

        log.info("开始处理第 {} 批数据，数据量: {}", currentBatchNumber, batch.size());

        try {
            // 1. 数据验证
            if (validationService != null) {
                ValidationResult validationResult = validateBatch(batch);
                if (validationResult != null && !validationResult.isPassed()) {
                    errors.addAll(validationResult.getErrors());
                    if ("valid".equals(target)) {
                        hasError = true;
                        log.warn("验证模式下发现错误，停止处理");
                        return;
                    }
                }
            }

            // 2. 坐标转换
            if (template.getIsZh() != null && template.getIsZh() && coordinateService != null) {
                transformCoordinates(batch);
            }

            // 3. 设置其他字段
            setAdditionalFields(batch);

            // 4. 数据插入
            if ("import".equals(target)) {
                insertBatch(batch);
            } else if ("export".equals(target)) {
                // 导出模式的特殊处理
                processExportBatch(batch);
            }

            successRecords += batch.size();
            log.info("第 {} 批数据处理完成", currentBatchNumber);

        } catch (Exception e) {
            log.error("处理第 {} 批数据时发生错误: {}", currentBatchNumber, e.getMessage());
            errorRecords += batch.size();
            hasError = true;
        } finally {
            // 清空当前批次
            batch.clear();
            currentBatchNumber++;
        }
    }

    /**
     * 将Excel行数据转换为GeoFeatureEntity
     */
    private GeoFeatureEntity convertToGeoFeatureEntity(Map<Integer, Object> rowData, int rowNumber) {
        GeoFeatureEntity entity = new GeoFeatureEntity();
        entity.setFeatureId("excel_row_" + rowNumber);
        entity.setCreatedAt(LocalDateTime.now());

        Map<String, Object> attributes = new HashMap<>();

        // 获取模板字段映射配置
        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings != null) {
            for (Map<String, Object> mapping : fieldMappings) {
                Integer position = (Integer) mapping.get("position");
                String fieldName = (String) mapping.get("fieldName");
                String fieldType = (String) mapping.get("fieldType");

                if (position != null && fieldName != null && rowData.containsKey(position)) {
                    Object value = rowData.get(position);
                    Object convertedValue = convertValue(value, fieldType);
                    attributes.put(fieldName, convertedValue);
                }
            }
        }

        // 处理坐标字段
        processCoordinateFields(rowData, attributes);

        entity.setRawAttributes(attributes);
        return entity;
    }

    /**
     * 处理坐标字段
     */
    private void processCoordinateFields(Map<Integer, Object> rowData, Map<String, Object> attributes) {
        // 处理点坐标（type = 2）
        if (template.getType() != null && template.getType() == 2) {
            Map<String, Object> pointMap = template.getPointMap();
            if (pointMap != null) {
                Integer xColumn = (Integer) pointMap.get("x");
                Integer yColumn = (Integer) pointMap.get("y");

                if (xColumn != null && yColumn != null) {
                    Object xValue = rowData.get(xColumn);
                    Object yValue = rowData.get(yColumn);

                    if (xValue != null && yValue != null) {
                        try {
                            double x = Double.parseDouble(xValue.toString());
                            double y = Double.parseDouble(yValue.toString());

                            // 验证坐标范围
                            validateCoordinateRange(x, y);

                            attributes.put("x_coord", x);
                            attributes.put("y_coord", y);

                            // 生成WKT几何
                            String wkt = String.format("POINT(%f %f)", x, y);
                            attributes.put("geometry", wkt);

                        } catch (NumberFormatException e) {
                            log.warn("坐标值格式错误: x={}, y={}", xValue, yValue);
                        }
                    }
                }
            }
        }
    }

    /**
     * 验证坐标范围
     */
    private void validateCoordinateRange(double x, double y) {
        String coordinateSystem = template.getOriginalCoordinateSystem();
        if (coordinateSystem != null) {
            // 根据坐标系验证坐标范围
            switch (coordinateSystem) {
                case "CGCS2000":
                case "BD09":
                    if (x < 119.5 || x > 121.4 || y < 26.9 || y > 28.7) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
                case "WenZhou2000":
                case "WenZhouCity":
                    if (x < 384095.61 || x > 571661.61 || y < 2977083.96 || y > 3176239.57) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
            }
        }
    }

    /**
     * 验证批次数据
     */
    private ValidationResult validateBatch(List<GeoFeatureEntity> entities) {
        try {
            if (validationService != null && !entities.isEmpty()) {
                // 使用现有的数据验证服务进行验证
                // 这里可以根据实际需要调用相应的验证方法
                log.debug("验证批次数据，数据量: {}", entities.size());

                // 创建简单的验证结果
                ValidationResult result = new ValidationResult();
                result.setPassed(true);
                result.setTotalRecords(entities.size());
                result.setErrorRecords(0);

                return result;
            }
        } catch (Exception e) {
            log.error("批次数据验证失败", e);
        }
        return null;
    }

    /**
     * 坐标转换
     */
    private void transformCoordinates(List<GeoFeatureEntity> entities) {
        String sourceCoordSystem = template.getOriginalCoordinateSystem();
        String targetCoordSystem = template.getTargetCoordinateSystem();

        if (sourceCoordSystem != null && targetCoordSystem != null && !sourceCoordSystem.equals(targetCoordSystem)) {
            for (GeoFeatureEntity entity : entities) {
                String geometry = entity.getGeometry();
                if (geometry != null && !geometry.trim().isEmpty()) {
                    try {
                        String transformedGeometry = coordinateService.transformGeometry(
                            geometry, sourceCoordSystem, targetCoordSystem);
                        entity.setGeometry(transformedGeometry);
                    } catch (Exception e) {
                        log.error("坐标转换失败，要素ID: {}, 错误: {}", entity.getFeatureId(), e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 设置附加字段
     */
    private void setAdditionalFields(List<GeoFeatureEntity> entities) {
        // 设置默认值、枚举值等
        for (GeoFeatureEntity entity : entities) {
            // 可以在这里添加业务逻辑设置默认字段
        }
    }

    /**
     * 插入批次数据
     */
    private void insertBatch(List<GeoFeatureEntity> entities) {
        if (batchInsertService != null) {
            batchInsertService.zeroConversionBatchInsert(entities, template);
        }
    }

    /**
     * 处理导出批次
     */
    private void processExportBatch(List<GeoFeatureEntity> entities) {
        // 导出模式的特殊处理逻辑
        log.debug("处理导出批次，数据量: {}", entities.size());
    }

    /**
     * 值类型转换
     */
    private Object convertValue(Object value, String fieldType) {
        if (value == null) {
            return null;
        }

        try {
            switch (fieldType.toLowerCase()) {
                case "string":
                case "varchar":
                case "text":
                    return value.toString();
                case "integer":
                case "int":
                    return Integer.valueOf(value.toString());
                case "double":
                case "float":
                case "decimal":
                    return Double.valueOf(value.toString());
                case "boolean":
                    return Boolean.valueOf(value.toString());
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("值转换失败，原始值: {}, 目标类型: {}", value, fieldType);
            return value;
        }
    }

    // Getter方法
    public boolean isHasError() {
        return hasError;
    }

    public int getTotalRecords() {
        return totalRecords;
    }

    public int getSuccessRecords() {
        return successRecords;
    }

    public int getErrorRecords() {
        return errorRecords;
    }

    public List<ValidationResult.ValidationError> getErrors() {
        return errors;
    }
}
