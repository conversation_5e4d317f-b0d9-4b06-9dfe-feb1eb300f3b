package com.zjxy.gisimportservice.mapper;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.util.TemplateFieldMappingUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 模板数据映射器 - 预构建映射关系，避免重复解析
 */
@Slf4j
@Component
public class TemplateDataMapper {

    @Autowired
    private TemplateFieldMappingUtil fieldMappingUtil;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 模板映射缓存
    private final Map<String, TemplateMapping> templateMappingCache = new ConcurrentHashMap<>();

    /**
     * 数据类型转换器枚举 - 预编译类型转换逻辑
     */
    public enum TypeConverter {
        DOUBLE(value -> {
            try {
                return Double.parseDouble(value.toString().trim());
            } catch (NumberFormatException e) {
                return null;
            }
        }),
        LONG(value -> {
            try {
                return Long.parseLong(value.toString().trim());
            } catch (NumberFormatException e) {
                return null;
            }
        }),
        BOOLEAN(value -> {
            String str = value.toString().trim().toLowerCase();
            return Boolean.parseBoolean(str) || "1".equals(str) || "true".equals(str);
        }),
        STRING(value -> value.toString());

        private final Function<Object, Object> converter;

        TypeConverter(Function<Object, Object> converter) {
            this.converter = converter;
        }

        public Object convert(Object value) {
            return value == null || value.toString().trim().isEmpty() ? null : converter.apply(value);
        }
    }

    /**
     * 字段转换器 - 预编译的字段映射和类型转换
     */
    public static class FieldConverter {
        @Getter
        private final String sourceFieldName;
        @Getter
        private final TypeConverter typeConverter;
        private final boolean isGeometryField;

        public FieldConverter(String sourceFieldName, TypeConverter typeConverter, boolean isGeometryField) {
            this.sourceFieldName = sourceFieldName;
            this.typeConverter = typeConverter;
            this.isGeometryField = isGeometryField;
        }

        public Object convert(GeoFeatureEntity entity) {
            if (isGeometryField) {
                return entity.getGeometry();
            }
            Object value = entity.getAttribute(sourceFieldName);
            return typeConverter.convert(value);
        }

        public boolean isGeometryField() { return isGeometryField; }
    }

    /**
     * 优化的模板映射信息 - 包含预编译转换器
     */
    public static class TemplateMapping {
        @Setter
        private String tableName;
        @Setter
        private String insertSQL;
        @Setter
        private List<String> fieldOrder;
        private Map<String, String> fieldMapping; // shpField -> dbField
        @Setter
        private Map<String, String> fieldTypeMapping; // dbField -> dbType
        @Setter
        private String geometryField;
        private String geometryPlaceholder;

        // 性能优化字段
        private FieldConverter[] fieldConverters; // 预编译的字段转换器数组
        private Map<String, String> reverseFieldMapping; // dbField -> shpField (O(1)查找)

        // Getters and Setters
        public String getTableName() { return tableName; }

        public String getInsertSQL() { return insertSQL; }

        public List<String> getFieldOrder() { return fieldOrder; }

        public Map<String, String> getFieldMapping() { return fieldMapping; }
        public void setFieldMapping(Map<String, String> fieldMapping) {
            this.fieldMapping = fieldMapping;
            // 构建反向映射以实现O(1)查找
            this.reverseFieldMapping = new HashMap<>();
            for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                this.reverseFieldMapping.put(entry.getValue(), entry.getKey());
            }
        }

        public Map<String, String> getFieldTypeMapping() { return fieldTypeMapping; }

        public String getGeometryField() { return geometryField; }

        public String getGeometryPlaceholder() { return geometryPlaceholder; }
        public void setGeometryPlaceholder(String geometryPlaceholder) { this.geometryPlaceholder = geometryPlaceholder; }

        // 性能优化方法
        public FieldConverter[] getFieldConverters() { return fieldConverters; }
        public void setFieldConverters(FieldConverter[] fieldConverters) { this.fieldConverters = fieldConverters; }

        public Map<String, String> getReverseFieldMapping() { return reverseFieldMapping; }

        /**
         * O(1) 获取源字段名
         */
        public String getSourceFieldName(String dbField) {
            return reverseFieldMapping.getOrDefault(dbField, dbField);
        }
    }

    /**
     * 获取或创建模板映射
     */
    public TemplateMapping getTemplateMapping(GisManageTemplate template) {
        String cacheKey = buildCacheKey(template);

        return templateMappingCache.computeIfAbsent(cacheKey, k -> {
            log.info("首次构建模板映射 - 模板: {} (ID: {})", template.getNameZh(), template.getId());
            return buildTemplateMapping(template);
        });
    }

    /**
     * 构建模板映射
     */
    private TemplateMapping buildTemplateMapping(GisManageTemplate template) {
        TemplateMapping mapping = new TemplateMapping();

        try {
            // 1. 构建表名
            String tableName = template.getTableName();
            if (template.getDataBaseMode() != null && !template.getDataBaseMode().trim().isEmpty()) {
                tableName = template.getDataBaseMode() + "." + tableName;
            }
            mapping.setTableName(tableName);

            // 2. 构建字段映射
            Map<String, String> fieldMapping = fieldMappingUtil.extractFieldMapping(template);
            mapping.setFieldMapping(fieldMapping);

            // 3. 构建字段类型映射
            Map<String, String> fieldTypeMapping = fieldMappingUtil.extractDbFieldTypeMapping(template);
            mapping.setFieldTypeMapping(fieldTypeMapping);

            // 4. 获取几何字段
            String geometryField = fieldMappingUtil.getGeometryFieldName(template);
            mapping.setGeometryField(geometryField);
            // 根据目标坐标系确定SRID
            String targetCoordSystem = template.getTargetCoordinateSystem();
            int srid = determineSRID(targetCoordSystem);
            mapping.setGeometryPlaceholder("ST_GeomFromText(?, " + srid + ")");

            // 5. 构建字段顺序
            List<String> dbFields = new ArrayList<>(fieldMapping.values());
            if (!dbFields.contains(geometryField)) {
                dbFields.add(geometryField);
            }
            mapping.setFieldOrder(dbFields);

            // 6. 构建INSERT SQL
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO ").append(tableName).append(" (");
            sql.append(String.join(", ", dbFields));
            sql.append(") VALUES (");

            List<String> placeholders = new ArrayList<>();
            for (String field : dbFields) {
                if (field.equals(geometryField)) {
                    placeholders.add(mapping.getGeometryPlaceholder());
                } else {
                    placeholders.add("?");
                }
            }
            sql.append(String.join(", ", placeholders));
            sql.append(")");

            mapping.setInsertSQL(sql.toString());

            // 7. 构建预编译字段转换器数组
            FieldConverter[] fieldConverters = buildFieldConverters(dbFields, fieldMapping, fieldTypeMapping, geometryField);
            mapping.setFieldConverters(fieldConverters);

            log.info("模板映射构建完成 - 表: {}, 字段数: {}, 几何字段: {}, 转换器数: {}",
                    tableName, dbFields.size(), geometryField, fieldConverters.length);
            log.debug("INSERT SQL: {}", mapping.getInsertSQL());

            return mapping;

        } catch (Exception e) {
            log.error("构建模板映射失败", e);
            throw new RuntimeException("构建模板映射失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建预编译字段转换器数组
     */
    private FieldConverter[] buildFieldConverters(List<String> fieldOrder,
                                                 Map<String, String> fieldMapping,
                                                 Map<String, String> fieldTypeMapping,
                                                 String geometryField) {
        FieldConverter[] converters = new FieldConverter[fieldOrder.size()];

        for (int i = 0; i < fieldOrder.size(); i++) {
            String dbField = fieldOrder.get(i);
            boolean isGeometry = dbField.equals(geometryField);

            if (isGeometry) {
                converters[i] = new FieldConverter(null, TypeConverter.STRING, true);
            } else {
                String sourceField = getSourceFieldNameFromMapping(dbField, fieldMapping);
                TypeConverter typeConverter = determineTypeConverter(dbField, fieldTypeMapping);
                converters[i] = new FieldConverter(sourceField, typeConverter, false);
            }
        }

        log.debug("构建了 {} 个字段转换器", converters.length);
        return converters;
    }

    /**
     * 从映射中获取源字段名（构建时使用）
     */
    private String getSourceFieldNameFromMapping(String dbField, Map<String, String> fieldMapping) {
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            if (entry.getValue().equals(dbField)) {
                return entry.getKey();
            }
        }
        return dbField;
    }

    /**
     * 确定类型转换器
     */
    private TypeConverter determineTypeConverter(String dbField, Map<String, String> fieldTypeMapping) {
        String fieldType = fieldTypeMapping.get(dbField);
        if (fieldType != null) {
            fieldType = fieldType.toLowerCase();

            if (fieldType.contains("double") || fieldType.contains("float") || fieldType.contains("real")) {
                return TypeConverter.DOUBLE;
            } else if (fieldType.contains("integer") || fieldType.contains("int") || fieldType.contains("bigint")) {
                return TypeConverter.LONG;
            } else if (fieldType.contains("boolean") || fieldType.contains("bool")) {
                return TypeConverter.BOOLEAN;
            }
        }
        return TypeConverter.STRING;
    }

    /**
     * 将实体转换为数据库参数数组 - 使用预编译转换器
     */
    public Object[] convertEntityToParams(GeoFeatureEntity entity, TemplateMapping mapping) {
        FieldConverter[] converters = mapping.getFieldConverters();
        Object[] params = new Object[converters.length];

        // 使用预编译转换器进行高性能转换
        for (int i = 0; i < converters.length; i++) {
            params[i] = converters[i].convert(entity);
        }

        return params;
    }

    /**
     * 批量转换实体为参数数组 - 高性能优化版本
     */
    public List<Object[]> convertEntitiesToParams(List<GeoFeatureEntity> entities, TemplateMapping mapping) {
        int entityCount = entities.size();
        FieldConverter[] converters = mapping.getFieldConverters();
        int fieldCount = converters.length;

        // 预分配容量，避免动态扩容
        List<Object[]> batchParams = new ArrayList<>(entityCount);

        // 批量转换，减少方法调用开销
        for (int i = 0; i < entityCount; i++) {
            GeoFeatureEntity entity = entities.get(i);
            Object[] params = new Object[fieldCount];

            // 内联转换逻辑，避免额外方法调用
            for (int j = 0; j < fieldCount; j++) {
                params[j] = converters[j].convert(entity);
            }

            batchParams.add(params);
        }

        log.debug("高性能批量参数转换完成 - 实体数: {}, 参数数组数: {}", entityCount, batchParams.size());
        return batchParams;
    }



    /**
     * 添加超高性能批量转换方法 - 针对大数据量优化
     */
    public Object[][] convertEntitiesToParamsArray(List<GeoFeatureEntity> entities, TemplateMapping mapping) {
        int entityCount = entities.size();
        FieldConverter[] converters = mapping.getFieldConverters();
        int fieldCount = converters.length;

        // 使用二维数组，减少对象创建开销
        Object[][] batchParams = new Object[entityCount][fieldCount];

        // 高性能批量转换
        for (int i = 0; i < entityCount; i++) {
            GeoFeatureEntity entity = entities.get(i);
            Object[] params = batchParams[i];

            for (int j = 0; j < fieldCount; j++) {
                params[j] = converters[j].convert(entity);
            }
        }

        log.debug("超高性能批量参数转换完成 - 实体数: {}, 字段数: {}", entityCount, fieldCount);
        return batchParams;
    }

    /**
     * 兼容性方法：将二维数组转换为List<Object[]>
     */
    public List<Object[]> convertArrayToList(Object[][] paramsArray) {
        List<Object[]> result = new ArrayList<>(paramsArray.length);
        Collections.addAll(result, paramsArray);
        return result;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(GisManageTemplate template) {
        return template.getTableName() + "_" +
               (template.getDataBaseMode() != null ? template.getDataBaseMode() : "default") + "_" +
               template.getId();
    }

    /**
     * 根据坐标系名称确定SRID
     */
    private int determineSRID(String coordinateSystem) {
        if (coordinateSystem == null) {
            return 4490; // 默认使用CGCS2000
        }

        switch (coordinateSystem.toUpperCase()) {
            case "CGCS2000":
            case "CGCS2000XY":
                return 4490; // CGCS2000
            case "WGS84":
                return 4326; // WGS84
            case "WENZHOUSCITY":
            case "WENZHOU2000":
                return 4490; // 温州坐标系通常转换为CGCS2000
            case "BEIJING1954":
                return 4214; // Beijing 1954
            default:
                log.warn("未知坐标系: {}, 使用默认SRID 4490 (CGCS2000)", coordinateSystem);
                return 4490; // 默认使用CGCS2000
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        templateMappingCache.clear();
        log.info("模板映射缓存已清除");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", templateMappingCache.size());
        stats.put("cachedTemplates", new ArrayList<>(templateMappingCache.keySet()));
        return stats;
    }
}
