package com.zjxy.gisimportservice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjxy.gisimportservice.entity.GisImportTask;

import java.util.List;
import java.util.Map;

/**
 * GIS数据导入任务服务接口
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
public interface GisImportTaskService extends IService<GisImportTask> {

    /**
     * 创建导入任务
     *
     * @param taskName 任务名称
     * @param templateId 模板ID
     * @param importFormat 导入格式
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @param createdBy 创建用户
     * @return 创建的任务
     */
    GisImportTask createImportTask(String taskName, Integer templateId,
                                  GisImportTask.ImportFormat importFormat,
                                  String filePath, Long fileSize, String createdBy);



    /**
     * 根据模板ID查询导入任务列表
     *
     * @param templateId 模板ID
     * @return 导入任务列表
     */
    List<GisImportTask> getTasksByTemplateId(Integer templateId);

    /**
     * 根据数据状态查询导入任务列表
     *
     * @param dataStatus 数据状态
     * @return 导入任务列表
     */
    List<GisImportTask> getTasksByDataStatus(GisImportTask.DataStatus dataStatus);

    /**
     * 分页查询导入任务列表
     *
     * @param page 分页参数
     * @param dataStatus 数据状态（可选）
     * @param importFormat 导入格式（可选）
     * @return 分页结果
     */
    IPage<GisImportTask> pageQuery(Page<GisImportTask> page,
                                  GisImportTask.DataStatus dataStatus,
                                  GisImportTask.ImportFormat importFormat);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param dataStatus 数据状态
     * @return 是否更新成功
     */
    boolean updateTaskStatus(Long taskId, GisImportTask.DataStatus dataStatus);


    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics();

    /**
     * 删除任务（包括相关文件）
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(Long taskId);

    /**
     * 获取任务详细信息（包括模板信息）
     *
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    Map<String, Object> getTaskDetails(Long taskId);

}
