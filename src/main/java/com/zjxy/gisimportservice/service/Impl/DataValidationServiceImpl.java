package com.zjxy.gisimportservice.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.ShapefileReaderService;
import com.zjxy.gisimportservice.util.TemplateFieldMappingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.FileDataStore;
import org.geotools.data.FileDataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 数据验证服务实现类
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Slf4j
@Service
public class DataValidationServiceImpl implements DataValidationService {

    @Autowired
    private GisImportTaskService gisImportTaskService;

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private TemplateFieldMappingUtil fieldMappingUtil;

    @Autowired
    private ShapefileReaderService shapefileReader;

    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 验证进度缓存
    private final Map<Long, Map<String, Object>> validationProgressCache = new ConcurrentHashMap<>();

    // 验证配置
    private final ValidationResult.ValidationConfig validationConfig = new ValidationResult.ValidationConfig();

    @Override
    @Async
    public CompletableFuture<ValidationResult> validateTaskDataAsync(Long taskId) {
        return CompletableFuture.supplyAsync(() -> validateTaskData(taskId));
    }

    @Override
    public ValidationResult validateTaskData(Long taskId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("开始验证任务数据 - 任务ID: {}", taskId);

        ValidationResult result = new ValidationResult();
        result.setStartTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
        result.setConfig(validationConfig);

        try {
            // 1. 获取任务信息
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                throw new IllegalArgumentException("任务不存在，任务ID: " + taskId);
            }

            // 2. 检查任务状态
            Map<String, Object> eligibilityCheck = checkValidationEligibility(taskId);
            if (!(Boolean) eligibilityCheck.get("eligible")) {
                throw new IllegalStateException((String) eligibilityCheck.get("message"));
            }

            // 3. 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
            if (template == null) {
                throw new IllegalArgumentException("模板不存在，模板ID: " + task.getTemplateId());
            }

            // 4. 执行数据验证（数据源切换通过注解方式在具体方法中处理）
            result = validateShapefileData(task.getFilePath(), template, task);

            // 5. 生成错误报告（如果有错误且错误数量不太多）
            if (result.getErrorRecords() > 0) {
                if (result.getErrorRecords() <= 1000000000) { // 限制错误报告的最大记录数
                    try {
                        String errorFilePath = generateErrorReportPath(taskId);
                        String generatedPath = generateErrorReport(result, errorFilePath);
                        result.setErrorFilePath(generatedPath);
                        log.info("生成错误报告: {}", generatedPath);
                    } catch (OutOfMemoryError e) {
                        log.error("生成错误报告时内存不足，跳过错误报告生成 - 任务ID: {}, 错误数量: {}", taskId, result.getErrorRecords());
                        result.setErrorFilePath("错误报告生成失败：内存不足");
                    } catch (Exception e) {
                        log.error("生成错误报告失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage());
                        result.setErrorFilePath("错误报告生成失败：" + e.getMessage());
                    }
                } else {
                    log.warn("错误数量过多({})，跳过错误报告生成 - 任务ID: {}", result.getErrorRecords(), taskId);
                    result.setErrorFilePath("错误数量过多，未生成详细报告");
                }
            }

            // 6. 更新任务状态和验证结果
            updateTaskValidationResult(task, result);

            log.info("任务数据验证完成 - 任务ID: {}, 通过: {}, 错误率: {}%",
                    taskId, result.isPassed(), result.getErrorRate());

        } catch (Exception e) {
            log.error("任务数据验证失败 - 任务ID: {}", taskId, e);
            result.setPassed(false);
            result.setErrorRecords(1);
            result.setSummary("验证过程异常: " + e.getMessage());
        } finally {
            result.setEndTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
            result.calculateDuration();
            result.generateSummary();

            // 清理进度缓存
            validationProgressCache.remove(taskId);
        }

        return result;
    }

    @Override
    public ValidationResult validateShapefileData(String filePath, GisManageTemplate template, GisImportTask task) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("开始验证Shapefile数据 - 文件: {}, 模板: {}", filePath, template.getNameZh());

        ValidationResult result = new ValidationResult();
        result.setStartTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
        result.setConfig(validationConfig);
        result.setErrors(new ArrayList<>());
        result.setErrorStatistics(new HashMap<>());

        try {
            // 1. 读取Shapefile数据
            List<SimpleFeature> features;
            SimpleFeatureType schema;

            // 直接从文件路径读取文件
            String actualFilePath = task.getFilePath();
            if (actualFilePath == null || actualFilePath.trim().isEmpty()) {
                throw new IOException("任务文件路径为空，任务ID: " + task.getId());
            }

            // 检查文件是否存在
            File file = new File(actualFilePath);
            if (!file.exists()) {
                throw new IOException("文件不存在: " + actualFilePath + "，任务ID: " + task.getId());
            }

            // 处理ZIP文件，使用模板中的工作表名称
            log.info("开始从ZIP文件读取要素: {}", actualFilePath);
            log.info("模板信息: ID={}, 名称={}, 工作表={}",
                    template.getId(), template.getNameZh(), template.getSheetName());

            Map<String, Object> zipResult = readFeaturesFromZip(actualFilePath, template);
            features = (List<SimpleFeature>) zipResult.get("features");
            schema = (SimpleFeatureType) zipResult.get("schema");

            log.info("ZIP文件读取结果: features={}, schema={}",
                    features != null ? features.size() : "null",
                    schema != null ? schema.getTypeName() : "null");

            // 保存SHP文件路径到验证结果中
            String shpFilePath = (String) zipResult.get("shpFilePath");
            if (shpFilePath != null) {
                result.setShpFilePath(shpFilePath);
                log.info("SHP文件路径已保存: {}", shpFilePath);
            }

            result.setTotalRecords(features.size());
            log.info("读取到 {} 条要素数据", features.size());

            // 2. 获取字段映射配置
            Map<String, String> fieldMapping = fieldMappingUtil.extractFieldMapping(template);
            Map<String, String> fieldTypeMapping = fieldMappingUtil.extractDbFieldTypeMapping(template);

            // 3. 初始化进度跟踪
            initializeValidationProgress(task.getId(), features.size());

            // 4. 批量验证数据
            int batchSize = validationConfig.getBatchSize();
            int processedCount = 0;
            int errorCount = 0;
            Set<Integer> recordsWithErrors = new HashSet<>(); // 记录有错误的记录索引

            for (int i = 0; i < features.size(); i += batchSize) {

                int endIndex = Math.min(i + batchSize, features.size());
                List<SimpleFeature> batch = features.subList(i, endIndex);

                // 验证批次数据
                BatchValidationResult batchResult = validateBatchWithRecordStats(
                        batch, schema, fieldMapping, fieldTypeMapping, i);

                result.getErrors().addAll(batchResult.getErrors());
                errorCount += batchResult.getErrors().size();
                recordsWithErrors.addAll(batchResult.getRecordsWithErrors());
                processedCount += batch.size();

                // 更新进度
                updateValidationProgress(task.getId(), processedCount, features.size(), errorCount);

                log.debug("验证进度: {}/{}, 字段错误数: {}, 记录错误数: {}",
                        processedCount, features.size(), errorCount, recordsWithErrors.size());
            }

            // 5. 计算验证结果
            result.setTotalRecords(features.size());
            result.setValidRecords(features.size() - recordsWithErrors.size());
            result.setErrorRecords(errorCount); // 字段级错误总数
            result.setRecordsWithErrors(recordsWithErrors.size()); // 有错误的记录数
            result.setTotalFields(fieldMapping.size()); // 验证的字段数量

            // 计算多种错误率
            calculateErrorRates(result);
            result.setPassed(result.isPassed());

            // 6. 统计错误类型
            calculateErrorStatistics(result);

            log.info("Shapefile数据验证完成 - 总数: {}, 错误: {}, 错误率: {}%",
                    features.size(), errorCount, result.getErrorRate());

        } catch (Exception e) {
            log.error("Shapefile数据验证失败", e);
            result.setPassed(false);
            result.setSummary("验证过程异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 创建验证错误对象
     */
    private ValidationResult.ValidationError createValidationError(
            int recordIndex, String featureId, String fieldName,
            ValidationResult.ErrorType errorType, String errorMessage,
            Object originalValue, String expectedType, String suggestion,
            ValidationResult.ErrorLevel errorLevel) {
        DynamicDataSourceManager.build().useDataSource("slave");

        ValidationResult.ValidationError error = new ValidationResult.ValidationError();
        error.setRecordIndex(recordIndex);
        error.setFeatureId(featureId);
        error.setFieldName(fieldName);
        error.setErrorType(errorType);
        error.setErrorMessage(errorMessage);

        // 安全处理原始值，避免几何对象序列化问题
        error.setOriginalValue(sanitizeOriginalValue(originalValue));

        error.setExpectedType(expectedType);
        error.setSuggestion(suggestion);
        error.setErrorLevel(errorLevel);
        return error;
    }

    /**
     * 清理原始值，避免序列化问题
     */
    private Object sanitizeOriginalValue(Object originalValue) {
        DynamicDataSourceManager.build().useDataSource("slave");
        if (originalValue == null) {
            return null;
        }

        // 如果是几何对象，转换为字符串表示
        if (originalValue instanceof com.vividsolutions.jts.geom.Geometry) {
            try {
                return originalValue.toString(); // WKT 格式
            } catch (Exception e) {
                return "Geometry[" + originalValue.getClass().getSimpleName() + "]";
            }
        }

        // 如果是复杂对象，转换为字符串
        if (originalValue.getClass().getPackage() != null &&
            originalValue.getClass().getPackage().getName().startsWith("com.vividsolutions")) {
            return originalValue.getClass().getSimpleName() + "@" + Integer.toHexString(originalValue.hashCode());
        }

        // 限制字符串长度，避免过长的值
        if (originalValue instanceof String) {
            String str = (String) originalValue;
            if (str.length() > 200) {
                return str.substring(0, 200) + "...";
            }
        }

        return originalValue;
    }

    /**
     * 检查字段是否为空
     */
    private boolean isEmpty(Object value) {
        if (value == null) return true;
        if (value instanceof String) return ((String) value).trim().isEmpty();
        return false;
    }

    /**
     * 检查类型兼容性
     */
    private boolean isTypeCompatible(Object value, String expectedType) {
        if (value == null || expectedType == null) return true;

        String actualType = value.getClass().getSimpleName().toLowerCase();
        String expected = expectedType.toLowerCase();

        // 基本类型兼容性检查
        switch (expected) {
            case "string":
            case "varchar":
            case "text":
                return true; // 任何类型都可以转换为字符串
            case "integer":
            case "int":
                return value instanceof Number || isNumericString(value.toString());
            case "double":
            case "float":
            case "decimal":
                return value instanceof Number || isNumericString(value.toString());
            case "boolean":
                return value instanceof Boolean || isBooleanString(value.toString());
            case "date":
            case "datetime":
                return value instanceof Date || isDateString(value.toString());
            default:
                return true; // 默认兼容
        }
    }

    /**
     * 检查是否为数字字符串
     */
    private boolean isNumericString(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查是否为布尔字符串
     */
    private boolean isBooleanString(String str) {
        return "true".equalsIgnoreCase(str) || "false".equalsIgnoreCase(str) ||
               "1".equals(str) || "0".equals(str) ||
               "yes".equalsIgnoreCase(str) || "no".equalsIgnoreCase(str);
    }

    /**
     * 检查是否为日期字符串
     */
    private boolean isDateString(String str) {
        // 简单的日期格式检查
        return str.matches("\\d{4}-\\d{2}-\\d{2}.*") ||
               str.matches("\\d{2}/\\d{2}/\\d{4}.*") ||
               str.matches("\\d{8}");
    }

    /**
     * 验证数据类型兼容性
     * 第二步：检查实际数据类型是否与模板定义一致
     */
    private ValidationResult.ValidationError validateDataTypeCompatibility(
            int recordIndex, String featureId, String shpFieldName, String dbFieldName,
            Object value, String expectedType) {
        DynamicDataSourceManager.build().useDataSource("slave");

        if (value == null || expectedType == null) {
            return null; // 空值不进行类型检查
        }

        String actualType = getActualDataType(value);
        String normalizedExpectedType = normalizeDataType(expectedType);

        // 检查基本类型兼容性
        if (!isBasicTypeCompatible(actualType, normalizedExpectedType)) {
            return createValidationError(recordIndex, featureId, shpFieldName,
                    ValidationResult.ErrorType.TYPE_MISMATCH,
                    String.format("数据类型不匹配：字段 '%s' 期望类型 '%s'，实际类型 '%s'",
                            dbFieldName, expectedType, actualType),
                    value, expectedType,
                    String.format("请将字段 '%s' 的数据转换为 %s 类型", dbFieldName, expectedType),
                    ValidationResult.ErrorLevel.ERROR);
        }

        return null;
    }

    /**
     * 验证类型转换
     * 第三步：检查数据是否能成功转换为目标类型
     */
    private ValidationResult.ValidationError validateTypeConversion(
            int recordIndex, String featureId, String shpFieldName, String dbFieldName,
            Object value, String expectedType) {
        DynamicDataSourceManager.build().useDataSource("slave");
        if (value == null || expectedType == null) {
            return null;
        }

        try {
            // 尝试进行类型转换
            Object convertedValue = attemptTypeConversion(value, expectedType);
            if (convertedValue == null) {
                return createValidationError(recordIndex, featureId, shpFieldName,
                        ValidationResult.ErrorType.CONVERSION_FAILED,
                        String.format("类型转换失败：无法将 '%s' (类型: %s) 转换为 %s",
                                value.toString(), getActualDataType(value), expectedType),
                        value, expectedType,
                        String.format("请检查字段 '%s' 的数据格式，确保可以转换为 %s 类型", dbFieldName, expectedType),
                        ValidationResult.ErrorLevel.ERROR);
            }
        } catch (Exception e) {
            return createValidationError(recordIndex, featureId, shpFieldName,
                    ValidationResult.ErrorType.CONVERSION_FAILED,
                    String.format("类型转换异常：%s", e.getMessage()),
                    value, expectedType,
                    String.format("请修复字段 '%s' 的数据格式", dbFieldName),
                    ValidationResult.ErrorLevel.ERROR);
        }

        return null;
    }

    /**
     * 验证数据格式
     */
    private ValidationResult.ValidationError validateDataFormat(
            int recordIndex, String featureId, String fieldName, Object value, String expectedType) {

        // 这里可以添加更详细的格式验证逻辑
        // 例如：邮箱格式、电话号码格式、身份证格式等

        return null; // 暂时返回null，表示格式验证通过
    }

    /**
     * 获取实际数据类型
     */
    private String getActualDataType(Object value) {
        if (value == null) return "null";

        Class<?> clazz = value.getClass();

        // 基本数据类型
        if (clazz == String.class) return "String";
        if (clazz == Integer.class || clazz == int.class) return "Integer";
        if (clazz == Long.class || clazz == long.class) return "Long";
        if (clazz == Double.class || clazz == double.class) return "Double";
        if (clazz == Float.class || clazz == float.class) return "Float";
        if (clazz == Boolean.class || clazz == boolean.class) return "Boolean";
        if (Date.class.isAssignableFrom(clazz)) return "Date";

        // JTS 几何类型特殊处理
        if (isJTSGeometry(value)) {
            return getJTSGeometryType(value);
        }

        return clazz.getSimpleName();
    }

    /**
     * 检查是否为 JTS 几何对象
     */
    private boolean isJTSGeometry(Object value) {
        if (value == null) return false;

        String className = value.getClass().getName();
        return className.startsWith("com.vividsolutions.jts.geom.") ||
               className.startsWith("org.locationtech.jts.geom.");
    }

    /**
     * 获取 JTS 几何对象的类型
     */
    private String getJTSGeometryType(Object value) {
        if (value == null) return "null";

        String className = value.getClass().getSimpleName();

        // 标准化几何类型名称
        switch (className.toLowerCase()) {
            case "point":
                return "Point";
            case "linestring":
                return "LineString";
            case "polygon":
                return "Polygon";
            case "multipoint":
                return "MultiPoint";
            case "multilinestring":
                return "MultiLineString";
            case "multipolygon":
                return "MultiPolygon";
            case "geometrycollection":
                return "GeometryCollection";
            default:
                return className;
        }
    }

    /**
     * 标准化数据类型名称
     */
    private String normalizeDataType(String dataType) {
        if (dataType == null) return "";

        String normalized = dataType.toLowerCase().trim();

        // 几何类型特殊处理
        if (isGeometryType(normalized)) {
            return normalizeGeometryType(normalized);
        }

        // 字符串类型
        if (normalized.contains("varchar") || normalized.contains("text") ||
            normalized.contains("char") || normalized.contains("string")) {
            return "string";
        }

        // 整数类型
        if (normalized.contains("int") || normalized.contains("integer")) {
            return "integer";
        }

        // 浮点数类型
        if (normalized.contains("double") || normalized.contains("float") ||
            normalized.contains("decimal") || normalized.contains("numeric")) {
            return "number";
        }

        // 布尔类型
        if (normalized.contains("bool")) {
            return "boolean";
        }

        // 日期时间类型
        if (normalized.contains("date") || normalized.contains("time")) {
            return "datetime";
        }

        return normalized;
    }

    /**
     * 检查是否为几何类型
     */
    private boolean isGeometryType(String dataType) {
        if (dataType == null) return false;

        String lower = dataType.toLowerCase();
        return lower.equals("point") || lower.equals("linestring") || lower.equals("polygon") ||
               lower.equals("multipoint") || lower.equals("multilinestring") || lower.equals("multipolygon") ||
               lower.equals("geometrycollection") || lower.equals("geometry") ||
               lower.contains("geom");
    }

    /**
     * 标准化几何类型名称
     */
    private String normalizeGeometryType(String geometryType) {
        if (geometryType == null) return "";

        String lower = geometryType.toLowerCase().trim();

        // 标准化几何类型名称
        switch (lower) {
            case "point":
                return "Point";
            case "linestring":
            case "line":
                return "LineString";
            case "polygon":
                return "Polygon";
            case "multipoint":
                return "MultiPoint";
            case "multilinestring":
            case "multiline":
                return "MultiLineString";
            case "multipolygon":
                return "MultiPolygon";
            case "geometrycollection":
                return "GeometryCollection";
            case "geometry":
            case "geom":
                return "Geometry";
            default:
                // 如果包含 geom 但不是标准类型，返回 Geometry
                if (lower.contains("geom")) {
                    return "Geometry";
                }
                return geometryType;
        }
    }

    /**
     * 检查基本类型兼容性
     */
    private boolean isBasicTypeCompatible(String actualType, String expectedType) {
        if (actualType == null || expectedType == null) return true;

        // 几何类型兼容性检查
        if (isGeometryTypeCompatible(actualType, expectedType)) {
            return true;
        }

        actualType = actualType.toLowerCase();
        expectedType = expectedType.toLowerCase();

        // 字符串类型兼容性
        if (expectedType.equals("string")) {
            return true; // 任何类型都可以转换为字符串
        }

        // 数字类型兼容性
        if (expectedType.equals("integer")) {
            return actualType.equals("integer") || actualType.equals("long") ||
                   actualType.equals("double") || actualType.equals("float");
        }

        if (expectedType.equals("number")) {
            return actualType.equals("integer") || actualType.equals("long") ||
                   actualType.equals("double") || actualType.equals("float");
        }

        // 布尔类型兼容性
        if (expectedType.equals("boolean")) {
            return actualType.equals("boolean") || actualType.equals("string");
        }

        // 日期类型兼容性
        if (expectedType.equals("datetime")) {
            return actualType.equals("date") || actualType.equals("string");
        }

        return true; // 默认兼容
    }

    /**
     * 检查几何类型兼容性
     */
    private boolean isGeometryTypeCompatible(String actualType, String expectedType) {
        if (actualType == null || expectedType == null) return false;

        // 标准化类型名称进行比较
        String normalizedActual = normalizeGeometryType(actualType);
        String normalizedExpected = normalizeGeometryType(expectedType);

        // 完全匹配
        if (normalizedActual.equalsIgnoreCase(normalizedExpected)) {
            return true;
        }

        // 通用几何类型兼容性
        if (normalizedExpected.equalsIgnoreCase("Geometry")) {
            return isGeometryType(actualType);
        }

        // 检查是否都是几何类型
        if (isGeometryType(actualType) && isGeometryType(expectedType)) {
            // 如果都是几何类型但不完全匹配，记录日志但允许通过
            log.debug("几何类型不完全匹配但兼容: 实际={}, 期望={}", actualType, expectedType);
            return true;
        }

        return false;
    }

    /**
     * 尝试类型转换
     */
    private Object attemptTypeConversion(Object value, String expectedType) {
        if (value == null) return null;

        String normalizedType = normalizeDataType(expectedType);

        try {
            switch (normalizedType) {
                case "string":
                    return value.toString();

                case "integer":
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    }
                    if (value instanceof String) {
                        return Integer.parseInt(((String) value).trim());
                    }
                    break;

                case "number":
                    if (value instanceof Number) {
                        return ((Number) value).doubleValue();
                    }
                    if (value instanceof String) {
                        return Double.parseDouble(((String) value).trim());
                    }
                    break;

                case "boolean":
                    if (value instanceof Boolean) {
                        return value;
                    }
                    if (value instanceof String) {
                        String str = ((String) value).trim().toLowerCase();
                        return "true".equals(str) || "1".equals(str) || "yes".equals(str);
                    }
                    break;

                case "datetime":
                    if (value instanceof Date) {
                        return value;
                    }
                    if (value instanceof String) {
                        // 尝试解析常见的日期格式
                        return parseDate((String) value);
                    }
                    break;
            }
        } catch (Exception e) {
            // 转换失败
            return null;
        }

        return value; // 无需转换或转换失败时返回原值
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        // 常见日期格式
        String[] patterns = {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "dd/MM/yyyy",
            "dd-MM-yyyy"
        };

        for (String pattern : patterns) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(pattern);
                return sdf.parse(dateStr.trim());
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }

        throw new RuntimeException("无法解析日期格式: " + dateStr);
    }

    /**
     * 验证几何数据
     */
    private ValidationResult.ValidationError validateGeometry(int recordIndex, String featureId, SimpleFeature feature) {
        try {
            Object geometry = feature.getDefaultGeometry();
            if (geometry == null) {
                return createValidationError(recordIndex, featureId, "geometry",
                        ValidationResult.ErrorType.GEOMETRY_INVALID,
                        "几何数据为空", null, "Geometry",
                        "请提供有效的几何数据", ValidationResult.ErrorLevel.ERROR);
            }

            // 增强的几何验证逻辑
            return validateGeometryDetails(recordIndex, featureId, geometry);

        } catch (Exception e) {
            return createValidationError(recordIndex, featureId, "geometry",
                    ValidationResult.ErrorType.GEOMETRY_INVALID,
                    "几何数据验证失败: " + e.getMessage(), null, "Geometry",
                    "请检查几何数据格式", ValidationResult.ErrorLevel.ERROR);
        }
    }

    /**
     * 详细的几何数据验证
     */
    private ValidationResult.ValidationError validateGeometryDetails(int recordIndex, String featureId, Object geometry) {
        try {
            // 1. 检查几何对象类型
            if (!isJTSGeometry(geometry)) {
                return createValidationError(recordIndex, featureId, "geometry",
                        ValidationResult.ErrorType.GEOMETRY_INVALID,
                        "几何对象类型不正确: " + geometry.getClass().getSimpleName(),
                        geometry, "JTS Geometry",
                        "请确保几何数据是有效的JTS几何对象", ValidationResult.ErrorLevel.ERROR);
            }

            // 2. 检查几何有效性（如果是JTS几何对象）
            if (geometry instanceof com.vividsolutions.jts.geom.Geometry) {
                com.vividsolutions.jts.geom.Geometry jtsGeom = (com.vividsolutions.jts.geom.Geometry) geometry;

                // 检查几何是否有效
                if (!jtsGeom.isValid()) {
                    return createValidationError(recordIndex, featureId, "geometry",
                            ValidationResult.ErrorType.GEOMETRY_INVALID,
                            "几何对象无效（可能存在自相交、重复点等问题）",
                            geometry, "Valid Geometry",
                            "请修复几何对象的拓扑错误", ValidationResult.ErrorLevel.WARNING);
                }

                // 3. 检查几何是否为空
                if (jtsGeom.isEmpty()) {
                    return createValidationError(recordIndex, featureId, "geometry",
                            ValidationResult.ErrorType.GEOMETRY_INVALID,
                            "几何对象为空",
                            geometry, "Non-empty Geometry",
                            "请提供非空的几何数据", ValidationResult.ErrorLevel.ERROR);
                }

                // 4. 检查坐标范围（基本的合理性检查）
                ValidationResult.ValidationError coordError = validateCoordinateRange(recordIndex, featureId, jtsGeom);
                if (coordError != null) {
                    return coordError;
                }
            }

            return null; // 验证通过

        } catch (Exception e) {
            return createValidationError(recordIndex, featureId, "geometry",
                    ValidationResult.ErrorType.GEOMETRY_INVALID,
                    "几何验证过程异常: " + e.getMessage(),
                    geometry, "Valid Geometry",
                    "请检查几何数据格式和内容", ValidationResult.ErrorLevel.ERROR);
        }
    }

    /**
     * 验证坐标范围的合理性
     */
    private ValidationResult.ValidationError validateCoordinateRange(int recordIndex, String featureId,
                                                                   com.vividsolutions.jts.geom.Geometry geometry) {
        try {
            com.vividsolutions.jts.geom.Coordinate[] coordinates = geometry.getCoordinates();

            for (com.vividsolutions.jts.geom.Coordinate coord : coordinates) {
                double x = coord.x;
                double y = coord.y;

                // 检查坐标是否为有效数值
                if (Double.isNaN(x) || Double.isNaN(y) || Double.isInfinite(x) || Double.isInfinite(y)) {
                    return createValidationError(recordIndex, featureId, "geometry",
                            ValidationResult.ErrorType.GEOMETRY_INVALID,
                            String.format("坐标包含无效数值: X=%f, Y=%f", x, y),
                            geometry, "Valid Coordinates",
                            "请确保所有坐标都是有效的数值", ValidationResult.ErrorLevel.ERROR);
                }

                // 基本的坐标范围检查（可根据实际需求调整）
                // 这里假设是地理坐标或投影坐标的合理范围
                if (Math.abs(x) > 1000000 || Math.abs(y) > 1000000) {
                    log.debug("坐标值较大，可能需要检查坐标系: X={}, Y={}", x, y);
                    // 不作为错误，只记录日志，因为投影坐标可能有很大的值
                }
            }

            return null; // 坐标范围验证通过

        } catch (Exception e) {
            return createValidationError(recordIndex, featureId, "geometry",
                    ValidationResult.ErrorType.GEOMETRY_INVALID,
                    "坐标范围验证失败: " + e.getMessage(),
                    geometry, "Valid Coordinates",
                    "请检查坐标数据", ValidationResult.ErrorLevel.WARNING);
        }
    }

    /**
     * 检查是否为必填字段
     */
    private boolean isRequiredField(String fieldName) {
        // 这里可以根据模板配置或业务规则判断字段是否必填
        // 暂时简单处理
        return fieldName != null && (fieldName.toLowerCase().contains("name") ||
                                   fieldName.toLowerCase().contains("id"));
    }

    /**
     * 计算错误统计
     */
    private void calculateErrorStatistics(ValidationResult result) {
        Map<String, Integer> statistics = result.getErrorStatistics();

        for (ValidationResult.ValidationError error : result.getErrors()) {
            String errorType = error.getErrorType().getDescription();
            statistics.put(errorType, statistics.getOrDefault(errorType, 0) + 1);
        }
    }

    /**
     * 初始化验证进度
     */
    private void initializeValidationProgress(Long taskId, int totalRecords) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Map<String, Object> progress = new HashMap<>();
        progress.put("taskId", taskId);
        progress.put("totalRecords", totalRecords);
        progress.put("processedRecords", 0);
        progress.put("errorRecords", 0);
        progress.put("progress", 0.0);
        progress.put("status", "VALIDATING");
        progress.put("startTime", Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));

        validationProgressCache.put(taskId, progress);
    }

    /**
     * 更新验证进度
     */
    private void updateValidationProgress(Long taskId, int processedRecords, int totalRecords, int errorRecords) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Map<String, Object> progress = validationProgressCache.get(taskId);
        if (progress != null) {
            progress.put("processedRecords", processedRecords);
            progress.put("errorRecords", errorRecords);
            progress.put("progress", (processedRecords * 100.0) / totalRecords);
            progress.put("lastUpdateTime", LocalDateTime.now());
        }
    }

    @Override
    public String generateErrorReport(ValidationResult validationResult, String outputPath) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("开始生成错误报告 - 输出路径: {}, 错误数量: {}", outputPath, validationResult.getErrors().size());

        // 如果错误数量太多，生成简化版本
        if (validationResult.getErrors().size() > 5000) {
            return generateSimplifiedErrorReport(validationResult, outputPath);
        }

        try {
            // 确保输出目录存在
            Path outputDir = Paths.get(outputPath).getParent();
            if (outputDir != null && !Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            // 使用SXSSFWorkbook进行流式写入，减少内存占用
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(1000)) { // 内存中只保留1000行
                workbook.setCompressTempFiles(true); // 压缩临时文件

                Sheet sheet = workbook.createSheet("数据验证错误报告");

                // 创建标题行
                Row headerRow = sheet.createRow(0);
                String[] headers = {
                    "记录索引", "要素ID", "错误字段", "错误类型", "错误描述",
                    "原始值", "期望类型", "建议修复方案", "错误级别"
                };

                CellStyle headerStyle = workbook.createCellStyle();
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                headerStyle.setFont(headerFont);

                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // 分批填充错误数据，避免内存溢出
                int rowIndex = 1;
                int batchSize = 500;
                List<ValidationResult.ValidationError> errors = validationResult.getErrors();

                for (int i = 0; i < errors.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, errors.size());
                    List<ValidationResult.ValidationError> batch = errors.subList(i, endIndex);

                    for (ValidationResult.ValidationError error : batch) {
                        Row row = sheet.createRow(rowIndex++);

                        row.createCell(0).setCellValue(error.getRecordIndex());
                        row.createCell(1).setCellValue(truncateString(error.getFeatureId(), 100));
                        row.createCell(2).setCellValue(truncateString(error.getFieldName(), 50));
                        row.createCell(3).setCellValue(truncateString(error.getErrorType().getDescription(), 50));
                        row.createCell(4).setCellValue(truncateString(error.getErrorMessage(), 200));
                        row.createCell(5).setCellValue(error.getOriginalValue() != null ?
                                truncateString(error.getOriginalValue().toString(), 100) : "");
                        row.createCell(6).setCellValue(truncateString(error.getExpectedType(), 50));
                        row.createCell(7).setCellValue(truncateString(error.getSuggestion(), 150));
                        row.createCell(8).setCellValue(truncateString(error.getErrorLevel().getDescription(), 20));
                    }

                    // 每处理一批数据后，强制垃圾回收
                    if (i % (batchSize * 4) == 0) {
                        System.gc();
                        log.debug("已处理错误记录: {}/{}", Math.min(i + batchSize, errors.size()), errors.size());
                    }
                }

                // 保存文件
                try (FileOutputStream fileOut = new FileOutputStream(outputPath)) {
                    workbook.write(fileOut);
                }

                // 清理临时文件
                workbook.dispose();

                log.info("错误报告生成成功 - 文件: {}, 错误数: {}", outputPath, validationResult.getErrors().size());
                return outputPath;
            }

        } catch (OutOfMemoryError e) {
            log.error("内存不足，尝试生成简化版错误报告 - 错误数量: {}", validationResult.getErrors().size());
            return generateSimplifiedErrorReport(validationResult, outputPath);
        } catch (Exception e) {
            log.error("生成错误报告失败", e);
            throw new RuntimeException("生成错误报告失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成简化版错误报告（用于大量错误的情况）
     */
    private String generateSimplifiedErrorReport(ValidationResult validationResult, String outputPath) {
        DynamicDataSourceManager.build().useDataSource("slave");
        try {
            // 修改文件名，标识为简化版
            String simplifiedPath = outputPath.replace(".xlsx", "_simplified.txt");

            // 确保输出目录存在
            Path outputDir = Paths.get(simplifiedPath).getParent();
            if (outputDir != null && !Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            try (FileWriter writer = new FileWriter(simplifiedPath)) {
                writer.write("数据验证错误报告（简化版）\n");
                writer.write("=================================\n");
                writer.write("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
                writer.write("总错误数: " + validationResult.getErrors().size() + "\n");
                writer.write("错误率: " + String.format("%.2f", validationResult.getErrorRate()) + "%\n\n");

                // 错误类型统计
                Map<String, Integer> errorTypeStats = new HashMap<>();
                for (ValidationResult.ValidationError error : validationResult.getErrors()) {
                    String errorType = error.getErrorType().getDescription();
                    errorTypeStats.put(errorType, errorTypeStats.getOrDefault(errorType, 0) + 1);
                }

                writer.write("错误类型统计:\n");
                for (Map.Entry<String, Integer> entry : errorTypeStats.entrySet()) {
                    writer.write(String.format("  %s: %d 个\n", entry.getKey(), entry.getValue()));
                }

                writer.write("\n前100个错误详情:\n");
                writer.write("----------------------------------------\n");

                // 只显示前100个错误
                int count = 0;
                for (ValidationResult.ValidationError error : validationResult.getErrors()) {
                    if (count >= 100) break;

                    writer.write(String.format("错误 %d:\n", count + 1));
                    writer.write(String.format("  记录索引: %d\n", error.getRecordIndex()));
                    writer.write(String.format("  要素ID: %s\n", truncateString(error.getFeatureId(), 50)));
                    writer.write(String.format("  错误字段: %s\n", error.getFieldName()));
                    writer.write(String.format("  错误类型: %s\n", error.getErrorType().getDescription()));
                    writer.write(String.format("  错误描述: %s\n", truncateString(error.getErrorMessage(), 100)));
                    writer.write("\n");
                    count++;
                }

                if (validationResult.getErrors().size() > 100) {
                    writer.write(String.format("... 还有 %d 个错误未显示\n", validationResult.getErrors().size() - 100));
                }
            }

            log.info("简化版错误报告生成成功 - 文件: {}, 错误数: {}", simplifiedPath, validationResult.getErrors().size());
            return simplifiedPath;

        } catch (Exception e) {
            log.error("生成简化版错误报告失败", e);
            return "错误报告生成失败: " + e.getMessage();
        }
    }

    /**
     * 截断字符串到指定长度
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) return "";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength - 3) + "...";
    }

    /**
     * 生成错误报告文件路径
     */
    private String generateErrorReportPath(Long taskId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = String.format("validation_errors_task_%d_%s.xlsx", taskId, timestamp);
        return Paths.get(tempPath, "validation-errors", fileName).toString();
    }

    /**
     * 更新任务验证结果（带重试机制和超时处理）
     */
    private void updateTaskValidationResult(GisImportTask task, ValidationResult result) {
        DynamicDataSourceManager.build().useDataSource("slave");
        int maxRetries = 3;
        int retryDelay = 5000; // 5秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试更新任务验证结果 - 任务ID: {}, 第{}次尝试", task.getId(), attempt);

                // 创建压缩版验证结果以减少数据量
                String validationResultJson = createCompactValidationResult(result);

                // 更新任务记录
                task.setValidationResult(validationResultJson);
                task.setRecordCount(result.getTotalRecords());
                task.setErrorCount(result.getErrorRecords());
                task.setProcessedCount(result.getValidRecords());

                // 根据验证结果更新任务状态
                if (result.isPassed()) {
                    task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED);
                }

                // 设置更新时间
                task.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));

                // 执行更新
                gisImportTaskService.updateById(task);

                log.info("任务验证结果已更新 - 任务ID: {}, 状态: {}, 第{}次尝试成功",
                        task.getId(), task.getDataStatus().getDescription(), attempt);
                return; // 成功，退出重试循环

            } catch (Exception e) {
                log.error("更新任务验证结果失败 - 任务ID: {}, 第{}次尝试失败: {}",
                         task.getId(), attempt, e.getMessage());

                if (attempt == maxRetries) {
                    // 最后一次尝试失败，保存最简化版结果
                    try {
                        saveMinimalValidationResult(task, result);
                        log.warn("已保存最简化版验证结果 - 任务ID: {}", task.getId());
                        return;
                    } catch (Exception ex) {
                        log.error("保存最简化版验证结果也失败 - 任务ID: {}", task.getId(), ex);
                        // 不抛出异常，避免影响验证流程
                        return;
                    }
                }

                // 等待后重试
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("重试被中断 - 任务ID: {}", task.getId());
                    return;
                }
            }
        }
    }

    /**
     * 创建压缩版验证结果
     */
    private String createCompactValidationResult(ValidationResult result) throws Exception {
        // 如果错误数量太多，只保留前50个错误以减少JSON大小
        if (result.getErrors().size() > 50) {
            ValidationResult compactResult = new ValidationResult();
            compactResult.setPassed(result.isPassed());
            compactResult.setTotalRecords(result.getTotalRecords());
            compactResult.setValidRecords(result.getValidRecords());
            compactResult.setErrorRecords(result.getErrorRecords());
            compactResult.setErrorRate(result.getErrorRate());
            compactResult.setStartTime(result.getStartTime());
            compactResult.setEndTime(result.getEndTime());
            compactResult.setDurationMs(result.getDurationMs());
            compactResult.setErrorFilePath(result.getErrorFilePath());
            compactResult.setErrorStatistics(result.getErrorStatistics());
            compactResult.setConfig(result.getConfig());
            compactResult.setSummary(String.format("验证完成 - 总计%d个错误，仅显示前50个", result.getErrorRecords()));

            // 只保留前50个错误
            compactResult.setErrors(result.getErrors().subList(0, Math.min(50, result.getErrors().size())));

            return objectMapper.writeValueAsString(compactResult);
        }

        return objectMapper.writeValueAsString(result);
    }

    /**
     * 保存最简化版验证结果
     */
    private void saveMinimalValidationResult(GisImportTask task, ValidationResult result) throws Exception {
        // 创建最简化的验证结果，只包含基本统计信息
        ValidationResult minimalResult = new ValidationResult();
        minimalResult.setPassed(result.isPassed());
        minimalResult.setTotalRecords(result.getTotalRecords());
        minimalResult.setValidRecords(result.getValidRecords());
        minimalResult.setErrorRecords(result.getErrorRecords());
        minimalResult.setErrorRate(result.getErrorRate());
        minimalResult.setSummary("验证完成 - 由于网络超时，详细结果已简化");
        minimalResult.setErrors(new ArrayList<>()); // 清空错误列表
        minimalResult.setErrorStatistics(new HashMap<>()); // 清空错误统计

        String minimalJson = objectMapper.writeValueAsString(minimalResult);

        task.setValidationResult(minimalJson);
        task.setRecordCount(result.getTotalRecords());
        task.setErrorCount(result.getErrorRecords());
        task.setProcessedCount(result.getValidRecords());
        task.setDataStatus(result.isPassed() ?
            GisImportTask.DataStatus.DATA_UNCHECKED : GisImportTask.DataStatus.DATA_UNCHECKED);
        task.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));

        gisImportTaskService.updateById(task);
    }

    @Override
    public Map<String, Object> getValidationProgress(Long taskId) {
        return validationProgressCache.getOrDefault(taskId, new HashMap<>());
    }

    @Override
    public boolean cancelValidation(Long taskId) {
        validationProgressCache.remove(taskId);
        log.info("取消验证任务 - 任务ID: {}", taskId);
        return true;
    }

    @Override
    public ValidationResult.ValidationConfig getValidationConfig() {
        return validationConfig;
    }

    @Override
    public Map<String, Object> checkValidationEligibility(Long taskId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Map<String, Object> result = new HashMap<>();

        try {
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                result.put("eligible", false);
                result.put("message", "任务不存在");
                return result;
            }

            // 允许所有状态的任务进行重新验证
            // 移除状态限制，支持重新验证功能
            log.info("任务状态检查 - 任务ID: {}, 当前状态: {}, 允许重新验证",
                    taskId, task.getDataStatus().getDescription());

            if (task.getFilePath() == null || task.getFilePath().trim().isEmpty()) {
                result.put("eligible", false);
                result.put("message", "任务文件路径为空");
                return result;
            }

            result.put("eligible", true);
            result.put("message", "任务可以进行验证");

        } catch (Exception e) {
            result.put("eligible", false);
            result.put("message", "检查验证资格失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从ZIP文件中读取Shapefile要素（重载方法，支持模板参数）
     */
    private Map<String, Object> readFeaturesFromZip(String zipFilePath, GisManageTemplate template) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<SimpleFeature> features = new ArrayList<>();
        SimpleFeatureType schema = null;

        // 检查ZIP文件是否存在
        File zipFile = new File(zipFilePath);
        if (!zipFile.exists()) {
            throw new IOException("ZIP文件不存在: " + zipFilePath);
        }

        // 创建临时目录 - 使用配置的临时路径
        Path tempDir = createConfiguredTempDirectory("shapefile_validation_");
        try {
            log.info("解压ZIP文件到临时目录: {}", tempDir);

            // 解压ZIP文件
            unzipFile(zipFilePath, tempDir.toFile());

            // 列出解压后的所有文件
            listDirectoryContents(tempDir.toFile(), "解压后的文件");

            // 根据模板的工作表名称查找SHP文件
            String sheetName = template != null ? template.getSheetName() : null;
            File shpFile;

            shpFile = findShpFile(tempDir.toFile(), sheetName);


            if (shpFile == null) {
                throw new IOException("在ZIP文件中未找到.shp文件: " + zipFilePath);
            }

            log.info("找到SHP文件: {}", shpFile.getAbsolutePath());

            // 读取Shapefile - 改进版本，支持中文文件名和更好的错误处理
            features = readShapefileWithEnhancedSupport(shpFile);
            schema = getShapefileSchema(shpFile);

            log.info("成功读取 {} 条要素数据", features.size());

            // 如果读取到0条数据，进行详细诊断
            if (features.isEmpty()) {
                performShapefileDiagnostics(shpFile);
            }

            // 将找到的SHP文件路径保存到结果中，供后续处理使用
            result.put("shpFilePath", shpFile.getAbsolutePath());

        } finally {
            // 清理临时目录
            try {
                Files.walk(tempDir)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
                log.info("清理临时目录: {}", tempDir);
            } catch (IOException e) {
                log.warn("清理临时目录失败: {}", tempDir, e);
            }
        }

        result.put("features", features);
        result.put("schema", schema);
        return result;
    }

    /**
     * 从ZIP文件中读取Shapefile要素（保留原方法用于兼容）
     */
    private Map<String, Object> readFeaturesFromZip(String zipFilePath) throws IOException {
        return readFeaturesFromZip(zipFilePath, null);
    }

    /**
     * 解压ZIP文件流
     */
    private void unzipStream(InputStream zipStream, File destDir) throws IOException {
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        try (ZipInputStream zis = new ZipInputStream(zipStream, Charset.forName("GBK"))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File file = new File(destDir, entry.getName());

                // 确保父目录存在
                File parent = file.getParentFile();
                if (parent != null && !parent.exists()) {
                    parent.mkdirs();
                }

                if (!entry.isDirectory()) {
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }
                }
                zis.closeEntry();
            }
        }
    }

    /**
     * 解压ZIP文件（保留原方法用于兼容）
     */
    private void unzipFile(String zipFilePath, File destDir) throws IOException {
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath), Charset.forName("GBK"))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File file = new File(destDir, entry.getName());

                // 确保父目录存在
                File parent = file.getParentFile();
                if (parent != null && !parent.exists()) {
                    parent.mkdirs();
                }

                if (!entry.isDirectory()) {
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }
                }
                zis.closeEntry();
            }
        }
    }

    /**
     * 根据工作表名称查找SHP文件
     */
    private File findShpFile(File directory, String sheetName) {
        if (sheetName == null || sheetName.trim().isEmpty()) {
            log.warn("工作表名称为空，使用默认查找逻辑");
            return null;
        }

        final String target = sheetName.trim();
        log.info("根据工作表名称查找SHP文件: {}", target);

        File[] files = directory.listFiles((dir, name) -> {
            String low = name.toLowerCase();
            return low.endsWith(".shp") &&
                    low.substring(0, low.length())
                            .equals(target.toLowerCase());
        });

        if (files != null && files.length > 0) {
            log.info("找到匹配的SHP文件: {}", files[0].getName());
            return files[0];
        }
        return null;
    }

    /**
     * 删除目录及其内容
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    /**
     * 批次验证结果内部类
     */
    private static class BatchValidationResult {
        private List<ValidationResult.ValidationError> errors = new ArrayList<>();
        private Set<Integer> recordsWithErrors = new HashSet<>();

        public List<ValidationResult.ValidationError> getErrors() {
            return errors;
        }

        public Set<Integer> getRecordsWithErrors() {
            return recordsWithErrors;
        }

        public void addError(ValidationResult.ValidationError error) {
            errors.add(error);
            recordsWithErrors.add(error.getRecordIndex());
        }
    }

    /**
     * 验证批次数据（增强版，包含记录级统计）
     */
    private BatchValidationResult validateBatchWithRecordStats(
            List<SimpleFeature> batch, SimpleFeatureType schema,
            Map<String, String> fieldMapping, Map<String, String> fieldTypeMapping, int startIndex) {

        BatchValidationResult batchResult = new BatchValidationResult();
        ValidationResult.ValidationConfig currentConfig = getValidationConfig();

        for (int i = 0; i < batch.size(); i++) {

//            if(i % 200 == 0){
//                ValidationResult.ValidationError er = createValidationError(1, "1", "shpFieldName",
//                        ValidationResult.ErrorType.TYPE_MISMATCH, "数据类型不匹配：字段 'testField' 期望类型 'String'，实际类型 'Integer'","Integer","String","请将字段数据转换为String类型", ValidationResult.ErrorLevel.ERROR);
//                batchResult.addError(er);
//            }

            SimpleFeature feature = batch.get(i);
            int recordIndex = startIndex + i;
            String featureId = feature.getID();

            // 验证每个映射字段
            for (Map.Entry<String, String> mapping : fieldMapping.entrySet()) {
                String shpFieldName = mapping.getKey();
                String dbFieldName = mapping.getValue();
                String expectedType = fieldTypeMapping.get(dbFieldName);

                try {
                    Object value = feature.getAttribute(shpFieldName);

                    // 数据类型兼容性验证
                    if (currentConfig.isCheckDataTypes() && value != null && expectedType != null) {
                        ValidationResult.ValidationError typeError = validateDataTypeCompatibility(
                                recordIndex, featureId, shpFieldName, dbFieldName, value, expectedType);
                        if (typeError != null) {
                            batchResult.addError(typeError);
                        }
                    }

                    // 类型转换验证
                    if (value != null && expectedType != null) {
                        ValidationResult.ValidationError conversionError = validateTypeConversion(
                                recordIndex, featureId, shpFieldName, dbFieldName, value, expectedType);
                        if (conversionError != null) {
                            batchResult.addError(conversionError);
                        }
                    }

                    // 数据格式验证
                    if (currentConfig.isCheckDataFormat() && value != null) {
                        ValidationResult.ValidationError formatError = validateDataFormat(
                                recordIndex, featureId, shpFieldName, value, expectedType);
                        if (formatError != null) {
                            batchResult.addError(formatError);
                        }
                    }

                } catch (Exception e) {
                    ValidationResult.ValidationError error = createValidationError(recordIndex, featureId, shpFieldName,
                            ValidationResult.ErrorType.FORMAT_ERROR,
                            "字段验证异常: " + e.getMessage(), null, expectedType,
                            "请检查数据格式", ValidationResult.ErrorLevel.CRITICAL);
                    batchResult.addError(error);
                }
            }

            // 验证几何数据
            if (validationConfig.isCheckGeometry()) {
                ValidationResult.ValidationError geometryError = validateGeometry(recordIndex, featureId, feature);
                if (geometryError != null) {
                    batchResult.addError(geometryError);
                }
            }
        }

        return batchResult;
    }

    /**
     * 计算多种错误率
     */
    private void calculateErrorRates(ValidationResult result) {
        int totalRecords = result.getTotalRecords();
        int errorRecords = result.getErrorRecords(); // 字段级错误总数
        int recordsWithErrors = result.getRecordsWithErrors(); // 有错误的记录数
        int totalFields = result.getTotalFields();

        if (totalRecords > 0) {
            // 原有的错误率（字段级错误率）
            result.setErrorRate((double) errorRecords / totalRecords * 100);

            // 记录错误率（有错误记录数 / 总记录数）
            result.setRecordErrorRate((double) recordsWithErrors / totalRecords * 100);

            // 字段错误率（总错误数 / (总记录数 × 字段数量)）
            if (totalFields > 0) {
                result.setFieldErrorRate((double) errorRecords / (totalRecords * totalFields) * 100);
            } else {
                result.setFieldErrorRate(0.0);
            }
        } else {
            result.setErrorRate(0.0);
            result.setRecordErrorRate(0.0);
            result.setFieldErrorRate(0.0);
        }

        log.info("错误率统计 - 字段级错误率: {:.2f}%, 记录错误率: {:.2f}%, 字段错误率: {:.2f}%",
                result.getErrorRate(), result.getRecordErrorRate(), result.getFieldErrorRate());
    }

    /**
     * 创建配置的临时目录
     */
    private Path createConfiguredTempDirectory(String prefix) throws IOException {
        // 确保配置的临时目录存在
        File tempBaseDir = new File(tempPath);
        if (!tempBaseDir.exists()) {
            boolean created = tempBaseDir.mkdirs();
            if (!created) {
                log.warn("无法创建配置的临时目录: {}, 使用系统默认临时目录", tempPath);
                return Files.createTempDirectory(prefix);
            }
        }

        // 在配置的目录下创建临时子目录
        Path tempDir = Files.createTempDirectory(tempBaseDir.toPath(), prefix);
        log.info("使用配置的临时目录: {}", tempDir);
        return tempDir;
    }

    /**
     * 增强的Shapefile读取方法，支持中文文件名和更好的错误处理
     */
    private List<SimpleFeature> readShapefileWithEnhancedSupport(File shpFile) throws IOException {
        List<SimpleFeature> features = new ArrayList<>();

        log.info("开始读取Shapefile: {}", shpFile.getAbsolutePath());
        log.info("文件大小: {} bytes", shpFile.length());

        // 检查相关文件是否存在
        checkShapefileCompleteness(shpFile);

        // 尝试多种编码方式读取
        String[] encodings = {"GBK", "UTF-8", "GB2312", "ISO-8859-1"};

        for (String encoding : encodings) {
            try {
                features = tryReadWithEncoding(shpFile, encoding);
                if (!features.isEmpty()) {
                    log.info("使用编码 {} 成功读取到 {} 条要素", encoding, features.size());
                    break;
                } else {
                    log.debug("使用编码 {} 读取到0条要素", encoding);
                }
            } catch (Exception e) {
                log.debug("使用编码 {} 读取失败: {}", encoding, e.getMessage());
            }
        }

        return features;
    }

    /**
     * 使用指定编码尝试读取Shapefile
     */
    private List<SimpleFeature> tryReadWithEncoding(File shpFile, String encoding) throws IOException {
        List<SimpleFeature> features = new ArrayList<>();

        // 方法1: 使用FileDataStoreFinder
        try {
            FileDataStore dataStore = FileDataStoreFinder.getDataStore(shpFile);
            if (dataStore != null) {
                try {
                    // 设置编码
                    if (dataStore instanceof org.geotools.data.shapefile.ShapefileDataStore) {
                        ((org.geotools.data.shapefile.ShapefileDataStore) dataStore).setCharset(Charset.forName(encoding));
                    }

                    SimpleFeatureSource featureSource = dataStore.getFeatureSource();
                    SimpleFeatureCollection featureCollection = featureSource.getFeatures();

                    try (SimpleFeatureIterator iterator = featureCollection.features()) {
                        while (iterator.hasNext()) {
                            features.add(iterator.next());
                        }
                    }

                    log.debug("方法1成功读取 {} 条要素 (编码: {})", features.size(), encoding);
                    return features;
                } finally {
                    dataStore.dispose();
                }
            }
        } catch (Exception e) {
            log.debug("方法1读取失败 (编码: {}): {}", encoding, e.getMessage());
        }

        // 方法2: 使用DataStoreFinder
        if (features.isEmpty()) {
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("url", shpFile.toURI().toURL());
                params.put("charset", encoding);

                DataStore dataStore = DataStoreFinder.getDataStore(params);
                if (dataStore != null) {
                    try {
                        String typeName = dataStore.getTypeNames()[0];
                        SimpleFeatureSource source = dataStore.getFeatureSource(typeName);
                        SimpleFeatureCollection collection = source.getFeatures();

                        try (SimpleFeatureIterator iterator = collection.features()) {
                            while (iterator.hasNext()) {
                                features.add(iterator.next());
                            }
                        }

                        log.debug("方法2成功读取 {} 条要素 (编码: {})", features.size(), encoding);
                    } finally {
                        dataStore.dispose();
                    }
                }
            } catch (Exception e) {
                log.debug("方法2读取失败 (编码: {}): {}", encoding, e.getMessage());
            }
        }

        return features;
    }

    /**
     * 获取Shapefile的Schema
     */
    private SimpleFeatureType getShapefileSchema(File shpFile) throws IOException {
        try {
            FileDataStore dataStore = FileDataStoreFinder.getDataStore(shpFile);
            if (dataStore != null) {
                try {
                    SimpleFeatureSource featureSource = dataStore.getFeatureSource();
                    return featureSource.getSchema();
                } finally {
                    dataStore.dispose();
                }
            }
        } catch (Exception e) {
            log.warn("获取Schema失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查Shapefile文件完整性
     */
    private void checkShapefileCompleteness(File shpFile) throws IOException {
        String baseName = shpFile.getName().substring(0, shpFile.getName().lastIndexOf('.'));
        File parentDir = shpFile.getParentFile();

        // 检查必需的文件
        String[] requiredExtensions = {".shp", ".shx", ".dbf"};
        String[] optionalExtensions = {".prj", ".cpg"};

        log.info("检查Shapefile文件完整性:");

        for (String ext : requiredExtensions) {
            File requiredFile = new File(parentDir, baseName + ext);
            if (requiredFile.exists()) {
                log.info("  ✓ {} 存在 (大小: {} bytes)", requiredFile.getName(), requiredFile.length());
            } else {
                throw new IOException("缺少必需的文件: " + requiredFile.getName());
            }
        }

        for (String ext : optionalExtensions) {
            File optionalFile = new File(parentDir, baseName + ext);
            if (optionalFile.exists()) {
                log.info("  ✓ {} 存在 (大小: {} bytes)", optionalFile.getName(), optionalFile.length());
            } else {
                log.info("  - {} 不存在 (可选文件)", baseName + ext);
            }
        }
    }

    /**
     * 执行Shapefile诊断
     */
    private void performShapefileDiagnostics(File shpFile) {
        log.warn("=== Shapefile诊断信息 ===");
        log.warn("文件路径: {}", shpFile.getAbsolutePath());
        log.warn("文件名: {}", shpFile.getName());
        log.warn("文件大小: {} bytes", shpFile.length());
        log.warn("文件是否可读: {}", shpFile.canRead());

        // 检查文件内容
        try {
            byte[] header = new byte[100];
            try (FileInputStream fis = new FileInputStream(shpFile)) {
                int bytesRead = fis.read(header);
                log.warn("读取到文件头 {} 字节", bytesRead);

                // 检查Shapefile魔数
                if (bytesRead >= 4) {
                    int magic = ((header[0] & 0xFF) << 24) | ((header[1] & 0xFF) << 16) |
                               ((header[2] & 0xFF) << 8) | (header[3] & 0xFF);
                    log.warn("文件魔数: 0x{} (期望: 0x0000270A)", Integer.toHexString(magic));

                    if (magic == 0x0000270A) {
                        log.warn("文件魔数正确，这是一个有效的Shapefile");
                    } else {
                        log.warn("文件魔数不正确，可能不是有效的Shapefile");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("读取文件头失败: {}", e.getMessage());
        }

        log.warn("=== 诊断结束 ===");
    }

    /**
     * 列出目录内容用于调试
     */
    private void listDirectoryContents(File directory, String description) {
        log.info("=== {} ===", description);
        log.info("目录路径: {}", directory.getAbsolutePath());

        if (!directory.exists()) {
            log.warn("目录不存在: {}", directory.getAbsolutePath());
            return;
        }

        File[] files = directory.listFiles();
        if (files == null || files.length == 0) {
            log.warn("目录为空: {}", directory.getAbsolutePath());
            return;
        }

        log.info("目录包含 {} 个文件/文件夹:", files.length);
        for (File file : files) {
            if (file.isDirectory()) {
                log.info("  [目录] {}", file.getName());
                // 递归列出子目录内容
                listDirectoryContents(file, "子目录: " + file.getName());
            } else {
                log.info("  [文件] {} (大小: {} bytes)", file.getName(), file.length());
            }
        }
        log.info("=== {} 结束 ===", description);
    }
}
