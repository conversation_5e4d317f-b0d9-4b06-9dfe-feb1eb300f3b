package com.zjxy.gisimportservice.service.Impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.mapper.GisImportTaskMapper;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GIS数据导入任务服务实现类
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Slf4j
@Service
@DS("slave")
public class GisImportTaskServiceImpl extends ServiceImpl<GisImportTaskMapper, GisImportTask>
        implements GisImportTaskService {

    @Autowired
    private GisManageTemplateService templateService;


    @Override
    @Transactional
    public GisImportTask createImportTask(String taskName, Integer templateId,
                                         GisImportTask.ImportFormat importFormat,
                                         String filePath, Long fileSize, String createdBy) {
        try {
            // 验证模板是否存在
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
            }

            // 创建导入任务
            GisImportTask task = new GisImportTask();
            task.setTaskName(taskName);
            task.setTemplateId(templateId);
            task.setImportFormat(importFormat);
            task.setDataStatus(GisImportTask.DataStatus.NOT_IMPORTED);
            task.setFilePath(filePath);
            task.setFileSize(fileSize);
            task.setCreatedBy(createdBy);
            task.setImportTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
            task.setProcessedCount(0);
            task.setErrorCount(0);

            // 保存到数据库
            boolean saved = save(task);
            if (!saved) {
                throw new RuntimeException("保存导入任务失败");
            }

            log.info("创建导入任务成功 - 任务ID: {}, 任务名称: {}, 模板ID: {}, 文件路径: {}",
                    task.getId(), taskName, templateId, filePath);

            return task;

        } catch (Exception e) {
            log.error("创建导入任务失败 - 任务名称: {}, 模板ID: {}, 错误: {}",
                     taskName, templateId, e.getMessage(), e);
            throw new RuntimeException("创建导入任务失败: " + e.getMessage(), e);
        }
    }


    @Override
    public List<GisImportTask> getTasksByTemplateId(Integer templateId) {
        return baseMapper.findByTemplateId(templateId);
    }

    @Override
    public List<GisImportTask> getTasksByDataStatus(GisImportTask.DataStatus dataStatus) {
        return baseMapper.findByDataStatus(dataStatus.getCode());
    }

    @Override
    public IPage<GisImportTask> pageQuery(Page<GisImportTask> page,
                                         GisImportTask.DataStatus dataStatus,
                                         GisImportTask.ImportFormat importFormat) {
        Integer statusCode = dataStatus != null ? dataStatus.getCode() : null;
        Integer formatCode = importFormat != null ? importFormat.getCode() : null;
        return baseMapper.pageQuery(page, statusCode, formatCode);
    }

    @Override
    @Transactional
    public boolean updateTaskStatus(Long taskId, GisImportTask.DataStatus dataStatus) {
        try {
            int updated = baseMapper.updateTaskStatus(taskId, dataStatus.getCode());
            log.info("更新任务状态 - 任务ID: {}, 状态: {}, 结果: {}",
                    taskId, dataStatus.getDescription(), updated > 0 ? "成功" : "失败");
            return updated > 0;
        } catch (Exception e) {
            log.error("更新任务状态失败 - 任务ID: {}, 状态: {}, 错误: {}",
                     taskId, dataStatus.getDescription(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 总任务数
            long totalTasks = count();
            stats.put("totalTasks", totalTasks);

            // 各状态任务数
            for (GisImportTask.DataStatus status : GisImportTask.DataStatus.values()) {
                QueryWrapper<GisImportTask> wrapper = new QueryWrapper<>();
                wrapper.eq("data_status", status.getCode());
                long count = count(wrapper);
                stats.put(status.name().toLowerCase() + "Count", count);
            }

            // 各格式任务数
            for (GisImportTask.ImportFormat format : GisImportTask.ImportFormat.values()) {
                QueryWrapper<GisImportTask> wrapper = new QueryWrapper<>();
                wrapper.eq("import_format", format.getCode());
                long count = count(wrapper);
                stats.put(format.name().toLowerCase() + "Count", count);
            }

            // 未完成任务数
            int pendingTasks = baseMapper.countPendingTasks();
            stats.put("pendingTasks", pendingTasks);

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            stats.put("error", "获取统计信息失败: " + e.getMessage());
        }

        return stats;
    }

    @Override
    @Transactional
    public boolean deleteTask(Long taskId) {
        try {
            // 获取任务信息
            GisImportTask task = getById(taskId);
            if (task == null) {
                log.warn("删除任务失败 - 任务不存在，任务ID: {}", taskId);
                return false;
            }

            // 删除相关文件
            if (task.getFilePath() != null) {
                try {
                    File file = new File(task.getFilePath());
                    if (file.exists()) {
                        file.delete();
                        log.info("删除任务文件: {}", task.getFilePath());
                    }
                } catch (Exception e) {
                    log.warn("删除任务文件失败: {}, 错误: {}", task.getFilePath(), e.getMessage());
                }
            }

            // 删除数据库记录
            boolean deleted = removeById(taskId);
            log.info("删除任务 - 任务ID: {}, 结果: {}", taskId, deleted ? "成功" : "失败");
            return deleted;

        } catch (Exception e) {
            log.error("删除任务失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTaskDetails(Long taskId) {
        Map<String, Object> details = new HashMap<>();

        try {
            GisImportTask task = getById(taskId);
            if (task == null) {
                details.put("error", "任务不存在");
                return details;
            }

            details.put("task", task);

            // 获取模板信息
            if (task.getTemplateId() != null) {
                GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
                details.put("template", template);
            }

            // 计算额外信息
            details.put("progressPercentage", task.getProgressPercentage());
            details.put("isCompleted", task.isCompleted());
            details.put("hasErrors", task.hasErrors());

        } catch (Exception e) {
            log.error("获取任务详细信息失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            details.put("error", "获取任务详细信息失败: " + e.getMessage());
        }
        return details;
    }

}
