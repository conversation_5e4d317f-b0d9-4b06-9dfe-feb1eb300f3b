package com.zjxy.gisimportservice.service.Impl;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.ShapefileReaderService;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.*;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.MultiLineString;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
@Service
public class ShapefileReaderServiceImpl implements ShapefileReaderService {

    @Autowired
    private CoordinateTransformService coordinateTransformService;

    @Autowired
    private HighPerformanceBatchInsertService highPerformanceBatchInsertService;

    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;

    private int totalFeaturesProcessed = 0;
    private int fieldMappingValidationCount = 0; // 字段映射验证计数器
    private boolean needCoordinateTransformCache = false; // 坐标转换需求缓存

    /**
     * 使用模板处理Shapefile ZIP文件
     */
    public int processShapefileZipWithTemplate(InputStream zipInputStream, String fileName,
                                              GisManageTemplate template) {
        Long startTime = System.currentTimeMillis();
        totalFeaturesProcessed = 0;
        fieldMappingValidationCount = 0; // 重置字段映射验证计数器
        // 预先计算坐标转换需求，避免每条记录都计算
        needCoordinateTransformCache = isCoordinateTransformNeeded(template);
        if (needCoordinateTransformCache) {
            log.info("启用坐标转换 - 源坐标系: {}, 目标坐标系: {}",
                    template.getOriginalCoordinateSystem(), template.getTargetCoordinateSystem());
        } else {
            log.info("跳过坐标转换，保留原始几何数据");
        }

        log.info("开始模板化处理Shapefile - 模板: {}", template.getNameZh());

        try {
            // 创建临时目录来存放解压后的文件 - 使用配置的临时路径
            Path tempDir = createConfiguredTempDirectory("shapefile-temp");
            unzip(zipInputStream, tempDir.toFile(), Charset.forName("GBK"));

            processShapefileWithTemplate(tempDir, template);

            // 删除临时目录及其内容
            deleteDirectory(tempDir.toFile());

        } catch (Exception e) {
            log.error("处理Shapefile ZIP文件失败", e);
            throw new RuntimeException("处理Shapefile ZIP文件失败", e);
        }

        Long endTime = System.currentTimeMillis();
        log.info("模板化处理完成，总共导入 {} 条记录，耗时：{}毫秒", totalFeaturesProcessed, (endTime - startTime));
        return totalFeaturesProcessed;
    }


    /**
     * 直接处理指定的SHP文件（避免重复查找）
    */
    private void processShapefileWithTemplate(Path tempDir, GisManageTemplate template) throws Exception {

        File shpFile = findShpFile(tempDir.toFile(),template.getSheetName());
        if (shpFile == null) {
            throw new FileNotFoundException("在ZIP文件中未找到.shp文件");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("url", shpFile.toURI().toURL());
        params.put("charset", Charset.forName("GBK").name());

        // 获取DataStore对象
        DataStore dataStore = null;
        try {
            dataStore = DataStoreFinder.getDataStore(params);
            if (dataStore == null) {
                throw new IOException("无法创建DataStore");
            }

            // 获取类型名称（通常是文件名）
            String typeName = dataStore.getTypeNames()[0];

            // 获取FeatureSource对象
            FeatureSource<SimpleFeatureType, SimpleFeature> source = dataStore.getFeatureSource(typeName);

            // 获取FeatureCollection对象
            FeatureCollection<SimpleFeatureType, SimpleFeature> collection = source.getFeatures();

            // 获取Schema
            SimpleFeatureType schema = source.getSchema();

            // 使用模板化的高性能批量处理
            log.info("=== 模板化处理模式 ===");
            log.info("模板ID: {}", template.getId());
            log.info("模板名称: {}", template.getNameZh());
            log.info("目标表: {}", template.getTableName());
            log.info("源坐标系: {}", template.getOriginalCoordinateSystem());
            log.info("目标坐标系: {}", template.getTargetCoordinateSystem());
            log.info("坐标转换: {}", (template.getIsZh() != null && template.getIsZh() ? "启用" : "禁用"));
            log.info("==================");

            processShapefileWithHighPerformanceBatching(collection, schema, template);

        } finally {
            // 关闭DataStore
            if (dataStore != null) {
                dataStore.dispose();
            }
        }
    }


    private void unzip(InputStream inputStream, File destDirectory, Charset charset) throws IOException {
        byte[] buffer = new byte[8 * 1024];
        try (ZipInputStream zis = new ZipInputStream(inputStream, charset)) {
            ZipEntry ze = zis.getNextEntry();
            while (ze != null) {
                String fileName = ze.getName();
                File newFile = new File(destDirectory, fileName);
                if (ze.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    newFile.getParentFile().mkdirs();
                    try (FileOutputStream fos = new FileOutputStream(newFile)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                zis.closeEntry();
                ze = zis.getNextEntry();
            }
        }
    }

    private File findShpFile(File directory, String sheetName) {
        if (sheetName == null || sheetName.trim().isEmpty()) {
            return null;
        }
        final String target = sheetName.trim();
        File[] files = directory.listFiles((dir, name) -> {
            String low = name.toLowerCase();
            return low.endsWith(".shp") &&
                    low.substring(0, low.length())
                            .equals(target.toLowerCase());
        });
        if (files != null && files.length > 0) {
            log.info("找到文件: {}", files[0].getName());
            return files[0];
        }
        return null;
    }

    private void deleteDirectory(File dir) {
        File[] allContents = dir.listFiles();
        if (allContents != null) {
            for (File file : allContents) {
                deleteDirectory(file);
            }
        }
        dir.delete();
    }

    /**
     * 高性能批量处理方案 - 使用模板化高性能插入
     */
    private void processShapefileWithHighPerformanceBatching(FeatureCollection<SimpleFeatureType, SimpleFeature> collection,
                                                            SimpleFeatureType schema,
                                                            GisManageTemplate template) throws Exception {
        final int LARGE_BATCH_SIZE = 20000; // 大批次大小
        final int THREAD_COUNT = Math.min(Runtime.getRuntime().availableProcessors() * 2, 16); // 最大线程数

        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        List<Future<Integer>> futures = new ArrayList<>();

        try (FeatureIterator<SimpleFeature> features = collection.features()) {
            int totalProcessed = 0;

            while (features.hasNext()) {
                // 收集一个大批次的数据
                List<SimpleFeature> largeBatch = new ArrayList<>(LARGE_BATCH_SIZE);
                int count = 0;

                while (count < LARGE_BATCH_SIZE && features.hasNext()) {
                    largeBatch.add(features.next());
                    count++;
                    totalProcessed++;
                }

                if (!largeBatch.isEmpty()) {
                    // 提交批次处理任务
                    Future<Integer> future = executor.submit(() -> processHighPerformanceBatch(largeBatch, schema, template));
                    futures.add(future);

                    log.info("提交高性能批次处理任务，批次大小: {}, 已提交批次数: {}, 累计处理: {} 条",
                            largeBatch.size(), futures.size(), totalProcessed);
                }
            }

            // 等待所有任务完成并统计结果
            int totalInserted = 0;
            for (Future<Integer> future : futures) {
                totalInserted += future.get(); // 等待任务完成并获取结果
            }

            totalFeaturesProcessed = totalInserted;
            log.info("所有高性能批次处理完成，总计插入: {} 条记录", totalInserted);

        } finally {
            executor.shutdown();
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        }
    }

    /**
     * 处理高性能批次数据 - 使用高性能批量插入服务
     */
    private Integer processHighPerformanceBatch(List<SimpleFeature> features, SimpleFeatureType schema,
                                               GisManageTemplate template) {
        long startTime = System.currentTimeMillis();

        try {
            // 转换为实体对象列表，支持MULTILINESTRING拆分
            List<GeoFeatureEntity> entities = new ArrayList<>();

            // 转换所有要素为实体对象，支持几何拆分
            log.info("开始处理批次，要素数量: {}", features.size());
            for (SimpleFeature feature : features) {
                // 使用新的几何处理方法，支持MULTILINESTRING拆分
                List<GeoFeatureEntity> processedFeatures = processGeometryField(feature, template);
                entities.addAll(processedFeatures);

                // 如果拆分了MULTILINESTRING，记录日志
                if (processedFeatures.size() > 1) {
                    log.info("要素 {} 的MULTILINESTRING被拆分为 {} 个LINESTRING",
                            feature.getID(), processedFeatures.size());
                } else {
                    log.debug("要素 {} 处理完成，几何类型: {}", feature.getID(),
                            processedFeatures.isEmpty() ? "空" : "单一几何");
                }
            }
            log.info("批次处理完成，原始要素: {}, 处理后实体: {}", features.size(), entities.size());
            // 使用高性能批量插入服务
            Map<String, Object> insertResult = highPerformanceBatchInsertService.zeroConversionBatchInsert(entities, template);

            boolean success = (Boolean) insertResult.getOrDefault("success", false);
            int insertedCount = 0;

            if (success) {
                insertedCount = (Integer) insertResult.getOrDefault("insertedCount", 0);
                log.debug("成功插入 {} 条记录到目标表: {}", insertedCount, template.getTableName());
            } else {
                String message = (String) insertResult.getOrDefault("message", "未知错误");
                log.warn("高性能插入失败: {}", message);
            }

            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;

            log.debug("高性能批次处理完成 - 数量: {}, 插入: {}, 耗时: {}ms",
                     entities.size(), insertedCount, processingTime);

            return insertedCount;

        } catch (Exception e) {
            log.error("高性能批次处理失败 - 线程: {}, 错误: {}",
                     Thread.currentThread().getName(), e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 将SimpleFeature转换为GeoFeatureEntity - 支持模板化转换
     */
    private GeoFeatureEntity convertFeatureToEntity(SimpleFeature feature, SimpleFeatureType schema,
                                                   GisManageTemplate template) {
        GeoFeatureEntity geoFeature = new GeoFeatureEntity();

        // 设置要素ID
        geoFeature.setFeatureId(feature.getID());

        // 优化几何数据处理
        Object geometryValue = feature.getDefaultGeometry();
        if (geometryValue != null) {
            try {
                String geometryStr = geometryValue.toString();

                if (needCoordinateTransformCache) {
                    // 需要坐标转换
                    String transformedGeometryStr = applyTemplateCoordinateTransform(geometryStr, template);
                    geoFeature.setGeometry(transformedGeometryStr);
                } else {
                    // 不需要坐标转换，直接使用原始几何数据
                    geoFeature.setGeometry(geometryStr);
                }

            } catch (Exception e) {
                log.warn("几何信息处理失败: {}", e.getMessage());
                geoFeature.setGeometry("GEOMETRY_ERROR");
            }
        }

        // 处理属性 - 优化版本，减少方法调用
        Map<String, Object> attributes = new HashMap<>(schema.getAttributeCount());
        for (int i = 0; i < schema.getAttributeCount(); i++) {
            String attributeName = schema.getDescriptor(i).getLocalName();
            Object attributeValue = feature.getAttribute(i); // 使用索引而不是名称，性能更好
            if (attributeValue != null) {
                attributes.put(attributeName, attributeValue);  // 保持原始类型
            }
        }

        // 字段映射验证采样 - 只对前3条数据进行验证，减少开销
        if (template != null && fieldMappingValidationCount < 3) {
            fieldMappingValidationCount++;
            validateFieldMappingSample(feature.getID(), attributes, template, fieldMappingValidationCount);
        }

        // 设置原始属性（保持原始数据类型）
        geoFeature.setRawAttributes(attributes);

        // 设置属性JSON（转换为字符串格式，用于兼容）
        Map<String, Object> stringAttributes = new HashMap<>();
        for (Map.Entry<String, Object> entry : attributes.entrySet()) {
            stringAttributes.put(entry.getKey(), entry.getValue() != null ? entry.getValue().toString() : null);
        }
        geoFeature.setAttributes(convertMapToJson(stringAttributes));

        return geoFeature;
    }

    /**
     * 判断是否需要坐标转换
     */
    private boolean isCoordinateTransformNeeded(GisManageTemplate template) {
        if (template == null) {
            return false; // 无模板时不转换
        }

        // 检查模板是否启用坐标转换
        if (template.getIsZh() == null || !template.getIsZh()) {
            return false;
        }

        // 检查坐标系配置
        String sourceCoordSystem = template.getOriginalCoordinateSystem();
        String targetCoordSystem = template.getTargetCoordinateSystem();

        if (sourceCoordSystem == null || sourceCoordSystem.trim().isEmpty() ||
            targetCoordSystem == null || targetCoordSystem.trim().isEmpty()) {
            return false;
        }

        // 检查源坐标系和目标坐标系是否相同
        if (sourceCoordSystem.equals(targetCoordSystem)) {
            return false;
        }

        return true;
    }

    /**
     * 字段映射验证采样 - 只对前5条数据进行精简输出
     */
    private void validateFieldMappingSample(String featureId, Map<String, Object> attributes,
                                          GisManageTemplate template,
                                          int sampleIndex) {
        try {
            if (sampleIndex == 1) {
                // 只在第一条记录时输出详细信息
                log.info("字段映射验证 - SHP字段: {}, 目标表: {}",
                        attributes.keySet(), template.getTableName());

                // 只显示前3个字段的值作为示例
                int fieldCount = 0;
                for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                    if (fieldCount >= 3) break;
                    log.debug("  示例字段 {} = {} ({})",
                            entry.getKey(),
                            entry.getValue(),
                            entry.getValue() != null ? entry.getValue().getClass().getSimpleName() : "null");
                    fieldCount++;
                }
            }

        } catch (Exception e) {
            log.warn("字段映射验证采样失败: {}", e.getMessage());
        }
    }

    /**
     * 应用模板化坐标转换
     * 根据模板中的originalCoordinateSystem，targetCoordinateSystem，isZh字段判断是否进行坐标转换
     */
    private String applyTemplateCoordinateTransform(String geometryWkt, GisManageTemplate template) {
        try {
            // 检查几何数据是否有效
            if (geometryWkt == null || geometryWkt.trim().isEmpty()) {
                log.warn("几何数据为空，无法进行坐标转换");
                return geometryWkt;
            }

            // 检查模板是否启用坐标转换
            if (template.getIsZh() == null || !template.getIsZh()) {
                log.debug("模板未启用坐标转换 (isZh={}), 返回原始几何数据", template.getIsZh());
                return geometryWkt; // 不进行转换
            }

            // 获取模板中的坐标系配置
            String sourceCoordSystem = template.getOriginalCoordinateSystem();
            String targetCoordSystem = template.getTargetCoordinateSystem();

            // 验证坐标系配置
            if (sourceCoordSystem == null || sourceCoordSystem.trim().isEmpty()) {
                log.warn("模板中源坐标系(originalCoordinateSystem)未配置，无法进行坐标转换");
                return geometryWkt;
            }

            if (targetCoordSystem == null || targetCoordSystem.trim().isEmpty()) {
                log.warn("模板中目标坐标系(targetCoordinateSystem)未配置，无法进行坐标转换");
                return geometryWkt;
            }

            // 检查源坐标系和目标坐标系是否相同
            if (sourceCoordSystem.equals(targetCoordSystem)) {
                log.debug("源坐标系和目标坐标系相同 ({})，无需转换", sourceCoordSystem);
                return geometryWkt;
            }

            // 检查坐标系是否受支持
            if (!coordinateTransformService.isSupportedCoordSystem(sourceCoordSystem)) {
                log.warn("源坐标系 {} 不受支持，无法进行坐标转换", sourceCoordSystem);
                return geometryWkt;
            }

            if (!coordinateTransformService.isSupportedCoordSystem(targetCoordSystem)) {
                log.warn("目标坐标系 {} 不受支持，无法进行坐标转换", targetCoordSystem);
                return geometryWkt;
            }

            log.debug("执行坐标转换: {} -> {}", sourceCoordSystem, targetCoordSystem);

            // 使用模板配置进行坐标转换
            String transformedWkt = coordinateTransformService.transformGeometry(
                geometryWkt, sourceCoordSystem, targetCoordSystem);

            // 检查转换结果
            if (transformedWkt == null) {
                log.warn("坐标转换返回null，使用原始几何数据");
                return geometryWkt;
            }

            return transformedWkt;

        } catch (Exception e) {
            log.error("模板化坐标转换失败: {}", e.getMessage(), e);
            return geometryWkt; // 返回原始数据
        }
    }

    private String convertMapToJson(Map<String, Object> map) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":\"")
                .append(entry.getValue().toString().replace("\"", "\\\""))
                .append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 使用配置的临时路径创建临时目录
     */
    private Path createConfiguredTempDirectory(String prefix) throws IOException {
        // 确保配置的临时路径目录存在
        Path configuredTempPath = Paths.get(tempPath);
        if (!Files.exists(configuredTempPath)) {
            Files.createDirectories(configuredTempPath);
            log.info("创建配置的临时目录: {}", configuredTempPath);
        }

        // 在配置的临时路径下创建子目录
        String timestamp = String.valueOf(System.currentTimeMillis());
        String dirName = prefix + "-" + timestamp;
        Path tempDir = configuredTempPath.resolve(dirName);

        Files.createDirectories(tempDir);
        log.info("创建临时工作目录: {}", tempDir);

        return tempDir;
    }

    /**
     * 拆分MULTILINESTRING为多个LINESTRING
     * @param wktGeometry WKT格式的几何字符串
     * @return 拆分后的LINESTRING列表，如果不是MULTILINESTRING则返回原始几何
     */
    private List<String> splitMultiLineStringToLineStrings(String wktGeometry) {
        List<String> result = new ArrayList<>();

        if (wktGeometry == null || wktGeometry.trim().isEmpty()) {
            return result;
        }

        try {
            WKTReader wktReader = new WKTReader();
            WKTWriter wktWriter = new WKTWriter();

            Geometry geometry = wktReader.read(wktGeometry);
            if (geometry instanceof MultiLineString) {
                MultiLineString multiLineString = (MultiLineString) geometry;
//                log.info("确认为MULTILINESTRING，包含 {} 条线段", multiLineString.getNumGeometries());

                // 拆分每个LineString
                for (int i = 0; i < multiLineString.getNumGeometries(); i++) {
                    Geometry lineGeometry = multiLineString.getGeometryN(i);
//                    log.info("处理第 {} 条线段，类型: {}", i + 1, lineGeometry.getClass().getSimpleName());

                    if (lineGeometry instanceof LineString) {
                        String lineWkt = wktWriter.write(lineGeometry);
                        result.add(lineWkt);
                    }
                }
            } else {
                result.add(wktGeometry);
            }

        } catch (ParseException e) {
            log.error("解析WKT几何失败: {}", wktGeometry, e);
            // 解析失败时返回原始几何
            result.add(wktGeometry);
        } catch (Exception e) {
            log.error("处理WKT几何时发生未知错误: {}", wktGeometry, e);
            // 发生其他错误时返回原始几何
            result.add(wktGeometry);
        }

        return result;
    }

    /**
     * 处理几何字段，如果是MULTILINESTRING则拆分为多个要素
     * @param originalFeature 原始要素
     * @param template 模板配置
     * @return 处理后的要素列表
     */
    private List<GeoFeatureEntity> processGeometryField(SimpleFeature originalFeature, GisManageTemplate template) {
        List<GeoFeatureEntity> results = new ArrayList<>();

        try {
            // 获取几何字段
            Object geometryObj = originalFeature.getDefaultGeometryProperty().getValue();
            if (geometryObj == null) {
                log.warn("要素 {} 的几何字段为空", originalFeature.getID());
                return results;
            }

            String wktGeometry = geometryObj.toString();

            // 拆分MULTILINESTRING
            List<String> geometries = splitMultiLineStringToLineStrings(wktGeometry);

            // 为每个几何创建要素
            for (int i = 0; i < geometries.size(); i++) {
                GeoFeatureEntity entity = new GeoFeatureEntity();

                // 设置要素ID，如果是拆分的要素则添加后缀
                String featureId = originalFeature.getID();
                if (geometries.size() > 1) {
                    featureId = featureId + "_part_" + (i + 1);
                }
                entity.setFeatureId(featureId);

                // 设置几何字段，支持坐标转换
                String geometryWkt = geometries.get(i);
                if (needCoordinateTransformCache && template != null) {
                    // 需要坐标转换
                    String transformedGeometryWkt = applyTemplateCoordinateTransform(geometryWkt, template);
                    entity.setGeometry(transformedGeometryWkt);
                } else {
                    // 不需要坐标转换，直接使用
                    entity.setGeometry(geometryWkt);
                }

                // 复制其他属性字段
                copyFeatureAttributes(originalFeature, entity, template);
                results.add(entity);
            }

        } catch (Exception e) {
            log.error("处理要素几何字段失败: {}", originalFeature.getID(), e);
            // 出错时创建一个基本要素
            GeoFeatureEntity entity = new GeoFeatureEntity();
            entity.setFeatureId(originalFeature.getID());
            copyFeatureAttributes(originalFeature, entity, template);
            results.add(entity);
        }

        return results;
    }

    /**
     * 复制要素属性到GeoFeatureEntity
     */
    private void copyFeatureAttributes(SimpleFeature feature, GeoFeatureEntity entity, GisManageTemplate template) {
        try {
            SimpleFeatureType schema = feature.getFeatureType();

            // 处理属性 - 使用与convertFeatureToEntity相同的逻辑
            Map<String, Object> attributes = new HashMap<>(schema.getAttributeCount());
            for (int i = 0; i < schema.getAttributeCount(); i++) {
                String attributeName = schema.getDescriptor(i).getLocalName();
                Object attributeValue = feature.getAttribute(i);
                if (attributeValue != null && !(attributeValue instanceof Geometry)) {
                    attributes.put(attributeName, attributeValue);
                }
            }

            // 设置属性到实体
            entity.setRawAttributes(attributes);

            // 如果需要字段映射验证，可以在这里添加
            if (template != null && fieldMappingValidationCount < 3) {
                fieldMappingValidationCount++;
                validateFieldMappingSample(feature.getID(), attributes, template, fieldMappingValidationCount);
            }

        } catch (Exception e) {
            log.error("复制要素属性失败: {}", feature.getID(), e);
        }
    }

    /**
     * 测试MULTILINESTRING拆分功能
     * 可以用于验证拆分逻辑是否正确
     */
//    public void testMultiLineStringSplit() {
//        try {
//            // 测试用的MULTILINESTRING WKT
//            String testMultiLineString ="MULTILINESTRING ((120.583761515452 27.7897907686495, 120.583736669834 27.789823869121), (120.583736669834 27.789823869121, 120.583711724213 27.7898579692925), (120.583711724213 27.7898579692925, 120.583686788593 27.7898890694639))";
//
//            log.info("测试MULTILINESTRING拆分功能");
//            log.info("原始几何: {}", testMultiLineString);
//
//            List<String> splitResults = splitMultiLineStringToLineStrings(testMultiLineString);
//
//            log.info("拆分结果数量: {}", splitResults.size());
//            for (int i = 0; i < splitResults.size(); i++) {
//                log.info("LineString {}: {}", i + 1, splitResults.get(i));
//            }
//
//            // 测试普通LINESTRING（不应该被拆分）
//            String testLineString = "LINESTRING(0 0, 1 1, 2 2)";
//            log.info("测试普通LINESTRING: {}", testLineString);
//
//            List<String> lineResults = splitMultiLineStringToLineStrings(testLineString);
//            log.info("普通LINESTRING结果数量: {}", lineResults.size());
//            if (!lineResults.isEmpty()) {
//                log.info("普通LINESTRING结果: {}", lineResults.get(0));
//            }
//
//            // 测试空几何
//            String emptyGeometry = "";
//            List<String> emptyResults = splitMultiLineStringToLineStrings(emptyGeometry);
//            log.info("空几何结果数量: {}", emptyResults.size());
//
//            log.info("MULTILINESTRING拆分测试完成");
//
//        } catch (Exception e) {
//            log.error("测试MULTILINESTRING拆分功能时发生错误", e);
//        }
//    }
}
