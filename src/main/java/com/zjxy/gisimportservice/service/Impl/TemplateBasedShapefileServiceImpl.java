package com.zjxy.gisimportservice.service.Impl;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.ShapefileReaderService;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;

/**
 * 基于模板的Shapefile处理服务实现类
 */
@Slf4j
@Service
@DS("slave")
public class TemplateBasedShapefileServiceImpl implements TemplateBasedShapefileService {

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private ShapefileReaderService shapefileReader;


    @Override
    public Map<String, Object> processShapefileWithTemplate(InputStream zipInputStream, String fileName, Integer templateId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始使用模板处理Shapefile，模板ID: {}, 文件名: {}", templateId, fileName);

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                result.put("success", false);
                result.put("message", "未找到指定的模板，模板ID: " + templateId);
                return result;
            }


            // 使用模板化的Shapefile处理器处理文件
            int featuresProcessed;
            featuresProcessed = shapefileReader.processShapefileZipWithTemplate(zipInputStream, fileName, template);


            // 检查处理结果
            boolean success = featuresProcessed > 0;
            String message = success ?
                "使用模板处理Shapefile成功" :
                "Shapefile处理失败，没有成功导入任何记录";

            // 生成处理报告
            Map<String, Object> report = generateProcessingReport(featuresProcessed, 0, template);

            result.put("success", success);
            result.put("message", message);
            result.put("templateId", templateId);
            result.put("templateName", template.getNameZh());
            result.put("featuresProcessed", featuresProcessed);
            result.put("report", report);

            if (success) {
                log.info("使用模板处理Shapefile完成，处理了 {} 条记录", featuresProcessed);
            } else {
                log.error("使用模板处理Shapefile失败，没有成功导入任何记录");
            }

        } catch (Exception e) {
            log.error("使用模板处理Shapefile失败", e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> processShapefileWithTemplateFromPath(String zipFilePath, Integer templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("从路径使用模板处理Shapefile，模板ID: {}, 文件路径: {}", templateId, zipFilePath);

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                result.put("success", false);
                result.put("message", "未找到指定的模板，模板ID: " + templateId);
                return result;
            }


            // 使用模板化的Shapefile处理器处理文件
            int featuresProcessed = 0;
            if (shapefileReader instanceof ShapefileReaderServiceImpl) {
                // 对于路径处理，需要先读取文件再调用模板方法
                try (FileInputStream fis = new FileInputStream(zipFilePath)) {
                    featuresProcessed = shapefileReader
                        .processShapefileZipWithTemplate(fis, new File(zipFilePath).getName(), template);
                }
            }

            // 检查处理结果
            boolean success = featuresProcessed > 0;
            String message = success ?
                "使用模板处理Shapefile成功" :
                "Shapefile处理失败，没有成功导入任何记录";

            // 生成处理报告
            Map<String, Object> report = generateProcessingReport(featuresProcessed, 0, template);

            result.put("success", success);
            result.put("message", message);
            result.put("templateId", templateId);
            result.put("templateName", template.getNameZh());
            result.put("featuresProcessed", featuresProcessed);
            result.put("report", report);

            if (success) {
                log.info("从路径使用模板处理Shapefile完成，处理了 {} 条记录", featuresProcessed);
            } else {
                log.error("从路径使用模板处理Shapefile失败，没有成功导入任何记录");
            }

        } catch (Exception e) {
            log.error("从路径使用模板处理Shapefile失败", e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> generateProcessingReport(int processedCount, int errorCount, GisManageTemplate template) {
        Map<String, Object> report = new HashMap<>();

        report.put("templateId", template.getId());
        report.put("templateName", template.getNameZh());
        report.put("processedCount", processedCount);
        report.put("errorCount", errorCount);
        report.put("successRate", errorCount == 0 ? 100.0 : (double) processedCount / (processedCount + errorCount) * 100);
        report.put("processTime", new Date());

        return report;
    }
}
