package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据验证服务接口
 * 提供完整的数据检查和验证功能
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
public interface DataValidationService {

    /**
     * 异步验证导入任务的数据
     *
     * @param taskId 任务ID
     * @return 异步验证结果
     */
    CompletableFuture<ValidationResult> validateTaskDataAsync(Long taskId);

    /**
     * 同步验证导入任务的数据
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    ValidationResult validateTaskData(Long taskId);

    /**
     * 验证Shapefile数据
     *
     * @param filePath 文件路径
     * @param template 模板配置
     * @param task 导入任务
     * @return 验证结果
     */
    ValidationResult validateShapefileData(String filePath, GisManageTemplate template, GisImportTask task);

    /**
     * 生成错误报告Excel文件
     *
     * @param validationResult 验证结果
     * @param outputPath 输出文件路径
     * @return 生成的文件路径
     */
    String generateErrorReport(ValidationResult validationResult, String outputPath);

    /**
     * 获取验证进度
     *
     * @param taskId 任务ID
     * @return 验证进度信息
     */
    Map<String, Object> getValidationProgress(Long taskId);

    /**
     * 取消验证任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelValidation(Long taskId);

    /**
     * 获取验证配置
     *
     * @return 验证配置
     */
    ValidationResult.ValidationConfig getValidationConfig();



    /**
     * 检查任务是否可以进行验证
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    Map<String, Object> checkValidationEligibility(Long taskId);

}
