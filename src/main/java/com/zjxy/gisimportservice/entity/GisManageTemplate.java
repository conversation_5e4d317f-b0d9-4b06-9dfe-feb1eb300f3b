package com.zjxy.gisimportservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjxy.gisimportservice.handler.ListMapToJsonHandler;
import com.zjxy.gisimportservice.handler.MapStringJsonTypeHandler;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * GIS数据导入模板实体类
 * 与gisresourcemanage项目保持完全一致的字段定义和注解
 */
@Data
@TableName(autoResultMap = true)
public class GisManageTemplate {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer tableId;

    private String tableName;

    private String datasourceName;

    private String nameZh;

    private String nameEn;

    /**
     * 表数据开始行号
     */
    private Integer thLine;

    private String filePath;
    /**
     * 线坐标来源，1来源于数据库，2来源于excel表
     */
    private Integer lineType;
    /**
     * 是否进行坐标转换
     */
    private Boolean isZh;
    /**
     * (模板类型，1纯文本，2点表， 3线表)
     */
    private Integer type;

    private String originalCoordinateSystem;

    private String targetCoordinateSystem;

    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> lineMap;


    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> pointMap;

    @TableField(typeHandler = ListMapToJsonHandler.class)
    private List<Map<String,Object>> map;

    private Date createTime;

    @TableField
    private String templateType;

    private String dataBase;

    private String dataBaseMode;

    private String dataBaseTable;

    private Boolean tsfl;
    /**
     * 图形多表
     */
    private Boolean txdb;

    private String uid;

    private String appId;

    @TableField(exist = false)
    private Integer field;

    @TableField(exist = false)
    private String value;

    private String groups;

    private String sheetName;

    private Boolean checkRule;

    private Integer checkRuleId;

    private String InOrOut;

    @TableField(typeHandler = ListMapToJsonHandler.class)
    private List<Map<String,Object>> valueMap;

    private String layerEn;
    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> association;

    @TableField(exist = false)
    private String fields;


    // ========== Excel配置相关的便利方法 ==========

    /**
     * 获取Excel配置
     * 从association字段中获取Excel相关配置
     */
    public Map<String, Object> getExcelConfig() {
        if (association != null && association.containsKey("excelConfig")) {
            Object excelConfigObj = association.get("excelConfig");
            if (excelConfigObj instanceof Map) {
                return (Map<String, Object>) excelConfigObj;
            }
        }
        return null;
    }

    /**
     * 设置Excel配置
     * 将Excel配置存储到association字段中
     */
    public void setExcelConfig(Map<String, Object> excelConfig) {
        if (association == null) {
            association = new java.util.HashMap<>();
        }

        if (excelConfig == null) {
            association.remove("excelConfig");
        } else {
            association.put("excelConfig", excelConfig);
        }
    }

    /**
     * 判断是否为Excel模板
     */
    public boolean isExcelTemplate() {
        return "excel".equalsIgnoreCase(this.templateType);
    }

    /**
     * 获取Excel批次大小
     */
    public Integer getExcelBatchSize() {
        Map<String, Object> config = getExcelConfig();
        if (config != null && config.containsKey("batchSize")) {
            return (Integer) config.get("batchSize");
        }
        return 1000; // 默认批次大小
    }

    /**
     * 获取Excel数据开始行号
     * 注意：EasyExcel的headRowNumber参数表示表头行数，数据从表头行+1开始读取
     * 如果thLine=1，说明第1行是数据，那么headRowNumber应该是0（无表头）
     * 如果thLine=2，说明第1行是表头，第2行是数据，那么headRowNumber应该是1
     */
    public Integer getExcelDataStartRow() {
        Map<String, Object> config = getExcelConfig();
        if (config != null && config.containsKey("dataStartRow")) {
            return (Integer) config.get("dataStartRow");
        }

        // 修正逻辑：如果thLine=1，说明第1行就是数据，headRowNumber=0
        // 如果thLine=2，说明第1行是表头，第2行是数据，headRowNumber=1
        if (this.thLine != null && this.thLine > 1) {
            return this.thLine - 1; // 表头行数 = 数据开始行 - 1
        }

        // 默认情况：第1行是表头，第2行开始是数据，所以headRowNumber=1
        return 1;
    }
}
