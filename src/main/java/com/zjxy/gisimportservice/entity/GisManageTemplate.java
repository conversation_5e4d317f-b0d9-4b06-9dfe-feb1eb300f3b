package com.zjxy.gisimportservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjxy.gisimportservice.handler.ListMapToJsonHandler;
import com.zjxy.gisimportservice.handler.MapStringJsonTypeHandler;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * GIS数据导入模板实体类
 * 与gisresourcemanage项目保持完全一致的字段定义和注解
 */
@Data
@TableName(autoResultMap = true)
public class GisManageTemplate {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer tableId;

    private String tableName;

    private String datasourceName;

    private String nameZh;

    private String nameEn;

    /**
     * 表数据开始行号
     */
    private Integer thLine;

    private String filePath;
    /**
     * 线坐标来源，1来源于数据库，2来源于excel表
     */
    private Integer lineType;
    /**
     * 是否进行坐标转换
     */
    private Boolean isZh;
    /**
     * (模板类型，1纯文本，2点表， 3线表)
     */
    private Integer type;

    private String originalCoordinateSystem;

    private String targetCoordinateSystem;

    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> lineMap;


    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> pointMap;

    @TableField(typeHandler = ListMapToJsonHandler.class)
    private List<Map<String,Object>> map;

    private Date createTime;

    @TableField
    private String templateType;

    private String dataBase;

    private String dataBaseMode;

    private String dataBaseTable;

    private Boolean tsfl;
    /**
     * 图形多表
     */
    private Boolean txdb;

    private String uid;

    private String appId;

    @TableField(exist = false)
    private Integer field;

    @TableField(exist = false)
    private String value;

    private String groups;

    private String sheetName;

    private Boolean checkRule;

    private Integer checkRuleId;

    private String InOrOut;

    @TableField(typeHandler = ListMapToJsonHandler.class)
    private List<Map<String,Object>> valueMap;

    private String layerEn;
    @TableField(typeHandler = MapStringJsonTypeHandler.class)
    private Map<String,Object> association;

    @TableField(exist = false)
    private String fields;

    // ========== 非数据库字段，用于业务处理 ==========

    @TableField(exist = false)
    private Integer field;

    @TableField(exist = false)
    private String value;

    @TableField(exist = false)
    private String fields;

    // ========== JSON字段的Java对象转换方法 ==========

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取字段映射配置
     */
    public List<Map<String, Object>> getMap() {
        if (mapJson == null || mapJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(mapJson, new TypeReference<List<Map<String, Object>>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析字段映射配置失败", e);
        }
    }

    /**
     * 设置字段映射配置
     */
    public void setMap(List<Map<String, Object>> map) {
        if (map == null) {
            this.mapJson = null;
            return;
        }
        try {
            this.mapJson = objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            throw new RuntimeException("序列化字段映射配置失败", e);
        }
    }

    /**
     * 获取线要素映射配置
     */
    public Map<String, Object> getLineMap() {
        if (lineMapJson == null || lineMapJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(lineMapJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析线要素映射配置失败", e);
        }
    }

    /**
     * 设置线要素映射配置
     */
    public void setLineMap(Map<String, Object> lineMap) {
        if (lineMap == null) {
            this.lineMapJson = null;
            return;
        }
        try {
            this.lineMapJson = objectMapper.writeValueAsString(lineMap);
        } catch (Exception e) {
            throw new RuntimeException("序列化线要素映射配置失败", e);
        }
    }

    /**
     * 获取点要素映射配置
     */
    public Map<String, Object> getPointMap() {
        if (pointMapJson == null || pointMapJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(pointMapJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析点要素映射配置失败", e);
        }
    }

    /**
     * 设置点要素映射配置
     */
    public void setPointMap(Map<String, Object> pointMap) {
        if (pointMap == null) {
            this.pointMapJson = null;
            return;
        }
        try {
            this.pointMapJson = objectMapper.writeValueAsString(pointMap);
        } catch (Exception e) {
            throw new RuntimeException("序列化点要素映射配置失败", e);
        }
    }

    /**
     * 获取值域映射配置
     */
    public List<Map<String, Object>> getValueMap() {
        if (valueMapJson == null || valueMapJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(valueMapJson, new TypeReference<List<Map<String, Object>>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析值域映射配置失败", e);
        }
    }

    /**
     * 设置值域映射配置
     */
    public void setValueMap(List<Map<String, Object>> valueMap) {
        if (valueMap == null) {
            this.valueMapJson = null;
            return;
        }
        try {
            this.valueMapJson = objectMapper.writeValueAsString(valueMap);
        } catch (Exception e) {
            throw new RuntimeException("序列化值域映射配置失败", e);
        }
    }

    /**
     * 获取关联配置
     */
    public Map<String, Object> getAssociation() {
        if (associationJson == null || associationJson.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(associationJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析关联配置失败", e);
        }
    }

    /**
     * 设置关联配置
     */
    public void setAssociation(Map<String, Object> association) {
        if (association == null) {
            this.associationJson = null;
            return;
        }
        try {
            this.associationJson = objectMapper.writeValueAsString(association);
        } catch (Exception e) {
            throw new RuntimeException("序列化关联配置失败", e);
        }
    }

    /**
     * 获取Excel配置
     * 从association字段中获取Excel相关配置
     */
    public Map<String, Object> getExcelConfig() {
        Map<String, Object> association = getAssociation();
        if (association != null && association.containsKey("excelConfig")) {
            Object excelConfigObj = association.get("excelConfig");
            if (excelConfigObj instanceof Map) {
                return (Map<String, Object>) excelConfigObj;
            }
        }
        return null;
    }

    /**
     * 设置Excel配置
     * 将Excel配置存储到association字段中
     */
    public void setExcelConfig(Map<String, Object> excelConfig) {
        Map<String, Object> association = getAssociation();
        if (association == null) {
            association = new HashMap<>();
        }

        if (excelConfig == null) {
            association.remove("excelConfig");
        } else {
            association.put("excelConfig", excelConfig);
        }

        setAssociation(association);
    }

    /**
     * 判断是否为Excel模板
     */
    public boolean isExcelTemplate() {
        return "excel".equalsIgnoreCase(this.templateType);
    }

    /**
     * 获取Excel批次大小
     */
    public Integer getExcelBatchSize() {
        Map<String, Object> config = getExcelConfig();
        if (config != null && config.containsKey("batchSize")) {
            return (Integer) config.get("batchSize");
        }
        return 1000; // 默认批次大小
    }

    /**
     * 获取Excel数据开始行号
     */
    public Integer getExcelDataStartRow() {
        Map<String, Object> config = getExcelConfig();
        if (config != null && config.containsKey("dataStartRow")) {
            return (Integer) config.get("dataStartRow");
        }
        return this.thLine != null ? this.thLine : 2; // 默认第2行开始
    }
}
