package com.zjxy.gisimportservice.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zjxy.gisimportservice.config.JsonbTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * GIS数据导入任务实体类
 * 用于管理和跟踪所有数据导入任务，支持分阶段导入流程
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("gis_import_task")
public class GisImportTask {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 导入任务创建时间（用户点击导入按钮的时间戳）
     */
    @TableField(value = "import_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Timestamp importTime;

    /**
     * 任务名称（使用上传的文件名作为默认值）
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 关联的模板ID（外键关联 gis_manage_template 表）
     */
    @TableField("template_id")
    private Integer templateId;

    /**
     * 导入数据格式
     * 1=SHP格式，2=Excel格式，3=CAD格式
     */
    @TableField("import_format")
    private ImportFormat importFormat;

    /**
     * 数据状态
     * 0=未导入，1=数据未检查，2=数据已导入
     */
    @TableField("data_status")
    private DataStatus dataStatus;

    /**
     * 原始文件存储路径（现在存储原始文件名）
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件数据（存储上传的文件内容，不存储到数据库）
     */
    @TableField(exist = false)
    private byte[] fileData;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 预计或实际导入的记录数量
     */
    @TableField("record_count")
    private Integer recordCount;

    /**
     * 已处理的记录数量
     */
    @TableField("processed_count")
    private Integer processedCount;

    /**
     * 错误记录数量
     */
    @TableField("error_count")
    private Integer errorCount;

    /**
     * 错误信息（如果导入失败）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 数据验证结果（JSON格式）
     */
    @TableField(value = "validation_result", typeHandler = JsonbTypeHandler.class)
    private String validationResult;

    /**
     * 处理日志信息
     */
    @TableField("processing_log")
    private String processingLog;

    /**
     * 创建用户
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 最后更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Timestamp updatedTime;

    /**
     * 开始处理时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Timestamp startTime;

    /**
     * 完成处理时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Timestamp endTime;

    /**
     * 处理耗时（毫秒）
     */
    @TableField("duration_ms")
    private Long durationMs;

    /**
     * 成功率（百分比）
     */
    @TableField("success_rate")
    private BigDecimal successRate;

    /**
     * 导入数据格式枚举
     */
    public enum ImportFormat {
        SHP(1, "SHP格式"),
        EXCEL(2, "Excel格式"),
        CAD(3, "CAD格式");

        @EnumValue
        private final int code;
        private final String description;

        ImportFormat(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ImportFormat fromCode(int code) {
            for (ImportFormat format : values()) {
                if (format.code == code) {
                    return format;
                }
            }
            throw new IllegalArgumentException("未知的导入格式代码: " + code);
        }
    }

    /**
     * 数据状态枚举
     */
    public enum DataStatus {
        NOT_IMPORTED(0, "未导入"),
        DATA_UNCHECKED(1, "数据未检查"),
        DATA_IMPORTED(2, "数据已导入");

        @EnumValue
        private final int code;
        private final String description;

        DataStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static DataStatus fromCode(int code) {
            for (DataStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的数据状态代码: " + code);
        }
    }

    /**
     * 计算成功率
     */
    public void calculateSuccessRate() {
        if (recordCount != null && recordCount > 0) {
            int successCount = recordCount - (errorCount != null ? errorCount : 0);
            this.successRate = BigDecimal.valueOf(successCount * 100.0 / recordCount)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算处理耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = endTime.getTime() - startTime.getTime();
        }
    }

    /**
     * 检查任务是否完成
     */
    public boolean isCompleted() {
        return dataStatus == DataStatus.DATA_IMPORTED;
    }

    /**
     * 检查任务是否有错误
     */
    public boolean hasErrors() {
        return errorCount != null && errorCount > 0;
    }

    /**
     * 获取处理进度百分比
     */
    public BigDecimal getProgressPercentage() {
        if (recordCount == null || recordCount == 0) {
            return BigDecimal.ZERO;
        }
        int processed = processedCount != null ? processedCount : 0;
        return BigDecimal.valueOf(processed * 100.0 / recordCount)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
