package com.zjxy.gisimportservice;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(scanBasePackages = "com.zjxy")
@MapperScan(basePackages = {
    "com.zjxy.framework.mapper",
    "com.zjxy.gisimportservice.mapper"
})
public class GisImportServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GisImportServiceApplication.class, args);
    }

}
