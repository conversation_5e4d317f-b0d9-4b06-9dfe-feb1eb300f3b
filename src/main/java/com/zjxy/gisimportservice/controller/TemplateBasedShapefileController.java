package com.zjxy.gisimportservice.controller;

import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.config.FileUploadConfig;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import com.zjxy.gisimportservice.service.Impl.ShapefileReaderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * 基于模板的Shapefile处理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/template-shapefile")
public class TemplateBasedShapefileController {

    @Autowired
    private TemplateBasedShapefileService templateBasedShapefileService;


    @Autowired
    private GisImportTaskService gisImportTaskService;

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private ShapefileReaderServiceImpl shapefileReaderService;

    /**
     * 使用模板上传并处理Shapefile
     */
    @PostMapping("/upload-with-template")
    public ResponseEntity<Map<String, Object>> uploadShapefileWithTemplate(
            @RequestParam("file") MultipartFile file,
            @RequestParam("templateId") Integer templateId) {

        Map<String, Object> response = new HashMap<>();

        if (file.isEmpty()) {
            response.put("success", false);
            response.put("message", "请选择一个ZIP文件上传");
            return ResponseEntity.badRequest().body(response);
        }

        if (templateId == null) {
            response.put("success", false);
            response.put("message", "请指定模板ID");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            log.info("开始使用模板处理Shapefile，模板ID: {}, 文件名: {}", templateId, file.getOriginalFilename());

            // 使用模板处理Shapefile
            Map<String, Object> result = templateBasedShapefileService.processShapefileWithTemplate(
                file.getInputStream(),
                file.getOriginalFilename(),
                templateId
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("使用模板处理Shapefile出错", e);
            response.put("success", false);
            response.put("message", "处理Shapefile出错: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 从路径使用模板处理Shapefile
     */
    @PostMapping("/process-with-template")
    public ResponseEntity<Map<String, Object>> processShapefileWithTemplate(

            @RequestParam("filePath") String filePath,
            @RequestParam("templateId") Integer templateId) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("从路径使用模板处理Shapefile，模板ID: {}, 文件路径: {}", templateId, filePath);

            // 使用模板处理Shapefile
            Map<String, Object> result = templateBasedShapefileService.processShapefileWithTemplateFromPath(filePath, templateId);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("使用模板处理Shapefile出错", e);
            response.put("success", false);
            response.put("message", "处理Shapefile出错: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取所有导入模板
     */
    @GetMapping("/templates")
    public ResponseEntity<Map<String, Object>> getAllImportTemplates() {
        Map<String, Object> response = new HashMap<>();

        try {
            List<GisManageTemplate> templates = templateService.getAllImportTemplates();

            response.put("success", true);
            response.put("data", templates);
            response.put("total", templates.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            response.put("success", false);
            response.put("message", "获取模板列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取模板详情
     */
    @GetMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> getTemplateById(@PathVariable Integer templateId) {
        Map<String, Object> response = new HashMap<>();

        try {
            GisManageTemplate template = templateService.getTemplateById(templateId);

            // 添加调试日志，检查JSON字段是否为空
            log.info("模板详情 - ID: {}, 名称: {}", template.getId(), template.getNameZh());
            log.info("JSON字段检查:");
            log.info("  mapJson: {}", template.getMapJson() != null ? "有数据" : "为空");
            log.info("  lineMapJson: {}", template.getLineMapJson() != null ? "有数据" : "为空");
            log.info("  pointMapJson: {}", template.getPointMapJson() != null ? "有数据" : "为空");
            log.info("  valueMapJson: {}", template.getValueMapJson() != null ? "有数据" : "为空");
            log.info("  associationJson: {}", template.getAssociationJson() != null ? "有数据" : "为空");

            if (template.getMapJson() != null) {
                log.info("  mapJson内容: {}", template.getMapJson());
            }

            response.put("success", true);
            response.put("data", template);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取模板详情失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "获取模板详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 创建新模板
     */
    @PostMapping("/templates")
    public ResponseEntity<Map<String, Object>> createTemplate(@RequestBody GisManageTemplate template) {
        Map<String, Object> response = new HashMap<>();

        try {
            Integer templateId = templateService.createTemplate(template);

            response.put("success", true);
            response.put("message", "模板创建成功");
            response.put("templateId", templateId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建模板失败", e);
            response.put("success", false);
            response.put("message", "创建模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新模板
     */
    @PutMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> updateTemplate(
            @PathVariable Integer templateId,
            @RequestBody GisManageTemplate template) {

        Map<String, Object> response = new HashMap<>();

        try {
            template.setId(templateId);
            Boolean success = templateService.updateTemplate(template);

            response.put("success", success);
            response.put("message", success ? "模板更新成功" : "模板更新失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("更新模板失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "更新模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> deleteTemplate(@PathVariable Integer templateId) {
        Map<String, Object> response = new HashMap<>();

        try {
            Boolean success = templateService.deleteTemplate(templateId);

            response.put("success", success);
            response.put("message", success ? "模板删除成功" : "模板删除失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("删除模板失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "删除模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据表名获取模板列表
     */
    @GetMapping("/templates/by-table-name/{tableName}")
    public ResponseEntity<Map<String, Object>> getTemplatesByTableName(@PathVariable String tableName) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<GisManageTemplate> templates = templateService.getTemplatesByTableName(tableName);

            response.put("success", true);
            response.put("data", templates);
            response.put("total", templates.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("根据表名获取模板列表失败，表名: {}", tableName, e);
            response.put("success", false);
            response.put("message", "获取模板列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }


    // ========== 导入任务管理API ==========//

    /**
     * 创建导入任务（第一阶段：任务创建）
     */
    @PostMapping("/create-import-task")
    public ResponseEntity<Map<String, Object>> createImportTask(
            @RequestParam("file") MultipartFile file,
            @RequestParam("templateId") Integer templateId,
            @RequestParam(value = "taskName", required = false) String taskName,
            @RequestParam(value = "createdBy", required = false, defaultValue = "system") String createdBy) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证文件
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择一个文件上传");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证模板
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                response.put("success", false);
                response.put("message", "模板不存在，模板ID: " + templateId);
                return ResponseEntity.badRequest().body(response);
            }

            // 确定任务名称
            if (taskName == null || taskName.trim().isEmpty()) {
                taskName = file.getOriginalFilename();
            }

            // 确定导入格式
            GisImportTask.ImportFormat importFormat = determineImportFormat(file.getOriginalFilename());

            // 保存文件到临时目录并获取完整路径
            String filePath = saveUploadedFileToTemp(file);

            // 创建导入任务，使用完整文件路径
            GisImportTask task = gisImportTaskService.createImportTask(
                taskName, templateId, importFormat, filePath, file.getSize(), createdBy);

            response.put("success", true);
            response.put("message", "导入任务创建成功");
            response.put("taskId", task.getId());
            response.put("taskName", task.getTaskName());
            response.put("templateId", templateId);
            response.put("templateName", template.getNameZh());
            response.put("importFormat", importFormat.getDescription());
            response.put("dataStatus", task.getDataStatus().getDescription());

            log.info("创建导入任务成功 - 任务ID: {}, 任务名称: {}, 模板: {}",
                    task.getId(), taskName, template.getNameZh());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建导入任务失败", e);
            response.put("success", false);
            response.put("message", "创建导入任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取导入任务列表
     */
    @GetMapping("/import-tasks")
    public ResponseEntity<Map<String, Object>> getImportTasks(
            @RequestParam(value = "templateId", required = false) Integer templateId,
            @RequestParam(value = "dataStatus", required = false) Integer dataStatus,
            @RequestParam(value = "importFormat", required = false) Integer importFormat) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<GisImportTask> tasks;

            if (templateId != null) {
                tasks = gisImportTaskService.getTasksByTemplateId(templateId);
            } else if (dataStatus != null) {
                GisImportTask.DataStatus status = GisImportTask.DataStatus.fromCode(dataStatus);
                tasks = gisImportTaskService.getTasksByDataStatus(status);
            } else {
                // 获取所有任务（限制数量）
                tasks = gisImportTaskService.list();
            }

            response.put("success", true);
            response.put("tasks", tasks);
            response.put("count", tasks.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取导入任务列表失败", e);
            response.put("success", false);
            response.put("message", "获取导入任务列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取任务详细信息
     */
    @GetMapping("/import-task/{taskId}")
    public ResponseEntity<Map<String, Object>> getTaskDetails(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> details = gisImportTaskService.getTaskDetails(taskId);

            if (details.containsKey("error")) {
                response.put("success", false);
                response.put("message", details.get("error"));
                return ResponseEntity.badRequest().body(response);
            }

            response.put("success", true);
            response.putAll(details);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取任务详细信息失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "获取任务详细信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/import-task/{taskId}/status")
    public ResponseEntity<Map<String, Object>> updateTaskStatus(
            @PathVariable Long taskId,
            @RequestParam("dataStatus") Integer dataStatus) {

        Map<String, Object> response = new HashMap<>();

        try {
            GisImportTask.DataStatus status = GisImportTask.DataStatus.fromCode(dataStatus);
            boolean updated = gisImportTaskService.updateTaskStatus(taskId, status);

            if (updated) {
                response.put("success", true);
                response.put("message", "任务状态更新成功");
                response.put("taskId", taskId);
                response.put("newStatus", status.getDescription());
            } else {
                response.put("success", false);
                response.put("message", "任务状态更新失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("更新任务状态失败 - 任务ID: {}, 状态: {}", taskId, dataStatus, e);
            response.put("success", false);
            response.put("message", "更新任务状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除导入任务
     */
    @DeleteMapping("/import-task/{taskId}")
    public ResponseEntity<Map<String, Object>> deleteTask(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean deleted = gisImportTaskService.deleteTask(taskId);

            if (deleted) {
                response.put("success", true);
                response.put("message", "任务删除成功");
            } else {
                response.put("success", false);
                response.put("message", "任务删除失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("删除任务失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "删除任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/import-tasks/statistics")
    public ResponseEntity<Map<String, Object>> getTaskStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> statistics = gisImportTaskService.getTaskStatistics();
            response.put("success", true);
            response.putAll(statistics);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取任务统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ========== 辅助方法 ==========

    // ========== 数据验证API ==========

    /**
     * 验证导入任务的数据
     */
    @PutMapping("/import-task/{taskId}/validate")
    public ResponseEntity<Map<String, Object>> validateTaskData(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("开始验证任务数据 - 任务ID: {}", taskId);

            // 1. 检查验证资格
            Map<String, Object> eligibilityCheck = dataValidationService.checkValidationEligibility(taskId);
            if (!(Boolean) eligibilityCheck.get("eligible")) {
                response.put("success", false);
                response.put("message", eligibilityCheck.get("message"));
                return ResponseEntity.badRequest().body(response);
            }

            // 2. 异步执行验证
            dataValidationService.validateTaskDataAsync(taskId)
                .thenAccept(validationResult -> {
                    log.info("任务验证完成 - 任务ID: {}, 通过: {}, 错误率: {}%",
                            taskId, validationResult.isPassed(), validationResult.getErrorRate());
                })
                .exceptionally(throwable -> {
                    log.error("任务验证异常 - 任务ID: {}", taskId, throwable);
                    return null;
                });

            response.put("success", true);
            response.put("message", "数据验证已启动，请查询验证进度");
            response.put("taskId", taskId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("启动数据验证失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "启动数据验证失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取验证进度
     */
    @GetMapping("/import-task/{taskId}/validation-progress")
    public ResponseEntity<Map<String, Object>> getValidationProgress(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> progress = dataValidationService.getValidationProgress(taskId);

            if (progress.isEmpty()) {
                // 检查任务是否已有验证结果
                GisImportTask task = gisImportTaskService.getById(taskId);
                if (task != null && task.getValidationResult() != null) {
                    response.put("success", true);
                    response.put("status", "COMPLETED");
                    response.put("message", "验证已完成");
                    response.put("hasResult", true);
                } else {
                    response.put("success", false);
                    response.put("message", "未找到验证进度信息");
                }
            } else {
                response.put("success", true);
                response.putAll(progress);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取验证进度失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "获取验证进度失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取验证结果
     */
    @GetMapping("/import-task/{taskId}/validation-result")
    public ResponseEntity<Map<String, Object>> getValidationResult(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                response.put("success", false);
                response.put("message", "任务不存在");
                return ResponseEntity.badRequest().body(response);
            }

            if (task.getValidationResult() == null) {
                response.put("success", false);
                response.put("message", "任务尚未进行验证");
                return ResponseEntity.badRequest().body(response);
            }

            // 解析验证结果
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            ValidationResult validationResult =
                objectMapper.readValue(task.getValidationResult(), ValidationResult.class);

            response.put("success", true);
            response.put("taskId", taskId);
            response.put("passed", validationResult.isPassed());
            response.put("totalRecords", validationResult.getTotalRecords());
            response.put("validRecords", validationResult.getValidRecords());
            response.put("errorRecords", validationResult.getErrorRecords());
            response.put("errorRate", validationResult.getErrorRate());
            response.put("summary", validationResult.getSummary());
            response.put("errorFilePath", validationResult.getErrorFilePath());
            response.put("errorStatistics", validationResult.getErrorStatistics());
            response.put("durationMs", validationResult.getDurationMs());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取验证结果失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "获取验证结果失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 下载错误报告
     */
    @GetMapping("/import-task/{taskId}/error-report")
    public ResponseEntity<?> downloadErrorReport(@PathVariable Long taskId) {
        try {
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "任务不存在");
                return ResponseEntity.badRequest().body(response);
            }

            if (task.getValidationResult() == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "任务尚未进行验证");
                return ResponseEntity.badRequest().body(response);
            }

            // 解析验证结果获取错误文件路径
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            ValidationResult validationResult =
                objectMapper.readValue(task.getValidationResult(), ValidationResult.class);

            String errorFilePath = validationResult.getErrorFilePath();
            if (errorFilePath == null || errorFilePath.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "没有错误报告文件");
                return ResponseEntity.badRequest().body(response);
            }

            // 检查文件是否存在
            java.io.File errorFile = new java.io.File(errorFilePath);
            if (!errorFile.exists()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "错误报告文件不存在");
                return ResponseEntity.badRequest().body(response);
            }

            // 返回文件下载响应
            org.springframework.core.io.Resource resource = new org.springframework.core.io.FileSystemResource(errorFile);

            return ResponseEntity.ok()
                    .header(org.springframework.http.HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"validation_errors_task_" + taskId + ".xlsx\"")
                    .header(org.springframework.http.HttpHeaders.CONTENT_TYPE,
                           "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(resource);

        } catch (Exception e) {
            log.error("下载错误报告失败 - 任务ID: {}", taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "下载错误报告失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 取消验证任务
     */
    @DeleteMapping("/import-task/{taskId}/validation")
    public ResponseEntity<Map<String, Object>> cancelValidation(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean cancelled = dataValidationService.cancelValidation(taskId);

            if (cancelled) {
                response.put("success", true);
                response.put("message", "验证任务已取消");
            } else {
                response.put("success", false);
                response.put("message", "取消验证任务失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("取消验证任务失败 - 任务ID: {}", taskId, e);
            response.put("success", false);
            response.put("message", "取消验证任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 根据文件名确定导入格式
     */
    private GisImportTask.ImportFormat determineImportFormat(String fileName) {
        if (fileName == null) {
            return GisImportTask.ImportFormat.SHP;
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".zip") || lowerFileName.endsWith(".shp")) {
            return GisImportTask.ImportFormat.SHP;
        } else if (lowerFileName.endsWith(".xlsx") || lowerFileName.endsWith(".xls")) {
            return GisImportTask.ImportFormat.EXCEL;
        } else if (lowerFileName.endsWith(".dwg") || lowerFileName.endsWith(".dxf")) {
            return GisImportTask.ImportFormat.CAD;
        } else {
            return GisImportTask.ImportFormat.SHP; // 默认
        }
    }

    /**
     * 保存上传的文件到临时目录
     */
    private String saveUploadedFileToTemp(MultipartFile file) throws IOException {
        // 检查配置是否可用
        if (!fileUploadConfig.isDirectoryAvailable()) {
            throw new IOException("上传目录不可用: " + fileUploadConfig.getTempDir());
        }

        // 使用配置的上传目录
        Path uploadDir = Paths.get(fileUploadConfig.getTempDir());

        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
            log.info("创建上传目录: {}", uploadDir.toAbsolutePath());
        }

        // 生成唯一的文件名（保留原始扩展名）
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        String fileName = System.currentTimeMillis() + "_" +
                         (originalFilename != null ? originalFilename : "upload" + extension);
        Path filePath = uploadDir.resolve(fileName);

        // 保存文件
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

        String savedPath = filePath.toAbsolutePath().toString();
        log.info("文件保存成功: {} -> {}", originalFilename, savedPath);

        return savedPath;
    }

}
