# 开发环境配置
# 文件上传配置（开发环境）
file:
  upload:
    # 自定义上传目录路径
    temp-dir: D:/xinyu_shixi/temp/gis-uploads
    # 最大文件大小
    max-file-size: 500MB
    # 最大请求大小
    max-request-size: 500MB

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # Servlet 配置（开发环境大文件支持）
  servlet:
    multipart:
      max-file-size: 20000MB
      max-request-size: 20000MB
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 数据源配置
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          #          url: **************************************************************************************************************
          url: ******************************************************************************************************
          username: postgres
          password: Xych-123
          driver-class-name: org.postgresql.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        slave:
          url: *****************************************************************************************************************************************************************************************
          username: postgres
          password: Xych-123
          driver-class-name: org.postgresql.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initialSize: 30              # 增加初始连接数
        minIdle: 15                  # 增加最小空闲连接
        maxActive: 150               # 增加最大活跃连接
        maxWait: 60000               # 增加等待时间
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        # 网络超时配置
        socketTimeout: 300000        # Socket读取超时 5分钟
        connectTimeout: 60000        # 连接超时 1分钟
        # 查询超时配置
        queryTimeout: 300            # 查询超时 5分钟
        # 事务超时配置
        transactionQueryTimeout: 300 # 事务查询超时 5分钟
        # 监控配置
        stat-view-servlet:
          enabled: true   # 启用监控页面
          url-pattern: /druid/*  # 访问路径
          login-username: admin  # 监控页登录账号
          login-password: admin  # 监控页密码
          reset-enable: false    # 禁用重置按钮
    druid: # 监控配置
      stat-view-servlet:
        enabled: true   # 启用监控页面
        url-pattern: /druid/*  # 访问路径
        login-username: admin  # 监控页登录账号
        login-password: admin  # 监控页密码
        reset-enable: false    # 禁用重置按钮

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    # 查询超时时间（秒）
    default-statement-timeout: 300
    # 日志配置 - 生产环境建议关闭详细SQL日志
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 类型别名包
    type-aliases-package: com.zjxy.gisdataimport.entity
    # 映射器扫描类型
    mapper-scan-type: ANNOTATION
    # 设置当查询结果值为null时，同样映射该查询字段给map
    call-setters-on-nulls: true
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0


# Druid 连接池额外配置
druid:
  maxPoolPreparedStatementPerConnectionSize: 100
  # PostgreSQL批处理优化
  connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=3000;rewriteBatchedStatements=true;useServerPrepStmts=false;cachePrepStmts=true;prepStmtCacheSize=500;prepStmtCacheSqlLimit=4096;defaultRowFetchSize=1000;useCompression=true

# GIS相关配置
gis:
  # 数据导入任务配置
  import:
    task:
      # 文件上传配置
      upload:
        # 临时文件存储路径
        temp-path: D:/xinyu_shixi/temp/gis-uploads
      validation:
        # 禁用必填字段检查
        check-required-fields: false
        # 其他验证选项
        check-data-types: true
        check-data-format: true
        check-geometry: true
        max-error-rate: 10
    # Excel导入配置
    excel:
      # Excel导入功能开关
      enabled: true
      # 默认批次大小
      default-batch-size: 1000
      # 最大文件大小（MB）
      max-file-size: 100
      # 支持的文件格式
      supported-formats: [".xlsx", ".xls"]
      # 临时文件存储路径
      temp-path: ${gis.import.task.upload.temp-path}/excel
      # 数据处理配置
      processing:
        # 是否启用异步处理
        async-enabled: true
        # 线程池大小
        thread-pool-size: 4
        # 队列大小
        queue-size: 1000
      # Excel特定验证配置
      validation:
        # 检查表头格式
        check-header-format: true
        # 检查数据一致性
        check-data-consistency: true
        # Excel最大错误率
        max-error-rate: 5
        # 是否严格模式
        strict-mode: false
  # 坐标转换配置
  coordinate:
    transform:
      enabled: true
      source-coord-system: WenZhou2000
      target-coord-system: CGCS2000
      failure-strategy: KEEP_ORIGINAL
  # 批量处理配置
  batch:
    size: 1000
    thread-pool-size: 4
    max-queue-size: 10000
  # 性能监控配置
  performance:
    monitor:
      enabled: true
      log-interval: 10000

# 日志配置
logging:
  level:
    # 应用程序日志级别
    com.zjxy.gisdataimport: INFO
    # MyBatis 日志级别
    com.zjxy.gisdataimport.mapper: WARN
    # Spring 框架日志级别
    org.springframework: WARN
    # 数据库连接池日志
    com.alibaba.druid: WARN
    # MyBatis-Plus 日志
    com.baomidou.mybatisplus: WARN
    # 根日志级别
    root: INFO
  pattern:
    # 控制台日志格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %c{1}:%L - %m%n"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %5p %c{1}:%L - %m%n"
