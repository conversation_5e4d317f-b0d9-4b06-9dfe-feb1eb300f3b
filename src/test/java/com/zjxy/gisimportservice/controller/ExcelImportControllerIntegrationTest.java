package com.zjxy.gisimportservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zjxy.gisimportservice.config.ExcelImportConfig;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.ExcelImportService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Excel导入控制器集成测试
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@WebMvcTest(ExcelImportController.class)
class ExcelImportControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ExcelImportService excelImportService;

    @MockBean
    private ExcelImportConfig excelConfig;

    @MockBean
    private GisManageTemplateService templateService;

    private MockMultipartFile mockExcelFile;

    @BeforeEach
    void setUp() {
        // 创建模拟Excel文件
        String excelContent = "Name,Value\nTest1,100.5\nTest2,200.3";
        mockExcelFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                excelContent.getBytes()
        );

        // 配置模拟对象
        when(excelConfig.isEnabled()).thenReturn(true);
        when(excelConfig.getDefaultBatchSize()).thenReturn(1000);
        when(excelConfig.getMaxFileSize()).thenReturn(100);
        when(excelConfig.getMaxFileSizeBytes()).thenReturn(100L * 1024 * 1024);
        when(excelConfig.getSupportedFormats()).thenReturn(Arrays.asList(".xlsx", ".xls"));

        ExcelImportConfig.ProcessingConfig processingConfig = new ExcelImportConfig.ProcessingConfig();
        processingConfig.setAsyncEnabled(true);
        processingConfig.setThreadPoolSize(4);
        processingConfig.setQueueSize(1000);
        when(excelConfig.getProcessing()).thenReturn(processingConfig);

        ExcelImportConfig.ValidationConfig validationConfig = new ExcelImportConfig.ValidationConfig();
        validationConfig.setCheckHeaderFormat(true);
        validationConfig.setCheckDataConsistency(true);
        validationConfig.setMaxErrorRate(5.0);
        validationConfig.setStrictMode(false);
        when(excelConfig.getValidation()).thenReturn(validationConfig);
    }

    @Test
    void testGetSheetNames_Success() throws Exception {
        // 模拟服务返回
        List<String> sheetNames = Arrays.asList("Sheet1", "Sheet2", "Sheet3");
        when(excelImportService.getSheetNames(any())).thenReturn(sheetNames);

        mockMvc.perform(multipart("/api/excel-import/sheets")
                .file(mockExcelFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取工作表名称成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(3));
    }

    @Test
    void testAnalyzeExcel_Success() throws Exception {
        // 模拟服务返回
        Map<String, Object> analysisResult = new HashMap<>();
        analysisResult.put("totalSheets", 1);
        analysisResult.put("fileName", "test.xlsx");
        analysisResult.put("fileSize", 1024L);

        List<Map<String, Object>> sheets = new ArrayList<>();
        Map<String, Object> sheet = new HashMap<>();
        sheet.put("name", "Sheet1");
        sheet.put("totalRows", 3);
        sheet.put("totalColumns", 2);
        sheets.add(sheet);
        analysisResult.put("sheets", sheets);

        when(excelImportService.analyzeExcelFile(any(), eq(1))).thenReturn(analysisResult);

        mockMvc.perform(multipart("/api/excel-import/analyze")
                .file(mockExcelFile)
                .param("headerRow", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Excel文件分析成功"))
                .andExpect(jsonPath("$.data.totalSheets").value(1))
                .andExpect(jsonPath("$.data.fileName").value("test.xlsx"));
    }

    @Test
    void testValidateExcel_Success() throws Exception {
        // 模拟服务返回
        Map<String, Object> validationResult = new HashMap<>();
        validationResult.put("valid", true);
        validationResult.put("totalRecords", 2);
        validationResult.put("errorRecords", 0);
        validationResult.put("errorRate", 0.0);
        validationResult.put("errors", new ArrayList<>());
        validationResult.put("warnings", new ArrayList<>());

        when(excelImportService.validateExcelData(any(), eq(1), eq("Sheet1")))
                .thenReturn(validationResult);

        mockMvc.perform(multipart("/api/excel-import/validate")
                .file(mockExcelFile)
                .param("templateId", "1")
                .param("sheetName", "Sheet1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Excel数据验证完成"))
                .andExpect(jsonPath("$.data.valid").value(true))
                .andExpect(jsonPath("$.data.totalRecords").value(2))
                .andExpect(jsonPath("$.data.errorRecords").value(0));
    }

    @Test
    void testAnalyzeExcelAttachment_Success() throws Exception {
        // 模拟服务返回
        Map<String, List<Map<String, Object>>> attachmentResult = new HashMap<>();

        when(excelImportService.analyzeExcelAttachment(any(), eq(1)))
                .thenReturn(attachmentResult);

        mockMvc.perform(multipart("/api/excel-import/analyze-attachment")
                .file(mockExcelFile)
                .param("templateId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Excel附件数据分析成功"));
    }

    @Test
    void testGetExcelImportConfig_Success() throws Exception {
        mockMvc.perform(get("/api/excel-import/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取配置信息成功"))
                .andExpect(jsonPath("$.data.enabled").value(true))
                .andExpect(jsonPath("$.data.maxFileSize").value(100))
                .andExpect(jsonPath("$.data.defaultBatchSize").value(1000));
    }

    @Test
    void testHealthCheck_Success() throws Exception {
        mockMvc.perform(get("/api/excel-import/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Excel导入服务运行正常"))
                .andExpect(jsonPath("$.enabled").value(true))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testBatchImportExcel_Success() throws Exception {
        // 模拟导入结果
        com.zjxy.gisimportservice.entity.ExcelImportResult importResult =
                com.zjxy.gisimportservice.entity.ExcelImportResult.builder()
                .success(true)
                .message("导入成功")
                .totalRecords(2)
                .successRecords(2)
                .errorRecords(0)
                .processingTimeMs(1000L)
                .build();

        when(excelImportService.batchImportExcelData(any(), eq(1), eq(500), eq("import"), eq("testUser")))
                .thenReturn(importResult);

        mockMvc.perform(multipart("/api/excel-import/batch-import")
                .file(mockExcelFile)
                .param("templateId", "1")
                .param("batchSize", "500")
                .param("target", "import")
                .param("createdBy", "testUser"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.totalRecords").value(2))
                .andExpect(jsonPath("$.data.successRecords").value(2))
                .andExpect(jsonPath("$.data.errorRecords").value(0));
    }

    @Test
    void testImportExcel_MissingFile() throws Exception {
        mockMvc.perform(multipart("/api/excel-import/import")
                .param("templateId", "1")
                .param("target", "import")
                .param("createdBy", "testUser"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testImportExcel_MissingTemplateId() throws Exception {
        mockMvc.perform(multipart("/api/excel-import/import")
                .file(mockExcelFile)
                .param("target", "import")
                .param("createdBy", "testUser"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testAnalyzeExcel_InvalidHeaderRow() throws Exception {
        when(excelImportService.analyzeExcelFile(any(), eq(-1)))
                .thenThrow(new IllegalArgumentException("表头行号无效"));

        mockMvc.perform(multipart("/api/excel-import/analyze")
                .file(mockExcelFile)
                .param("headerRow", "-1"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }
}
