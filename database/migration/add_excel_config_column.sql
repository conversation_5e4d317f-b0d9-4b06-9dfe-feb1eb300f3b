-- 为 gis_manage_template 表添加 excel_config 列
-- 用于存储 Excel 导入相关的配置信息

-- 添加 excel_config 列
ALTER TABLE gis_manage_template 
ADD COLUMN excel_config TEXT;

-- 添加列注释
COMMENT ON COLUMN gis_manage_template.excel_config IS 'Excel配置信息，JSON格式存储，包含Sheet名称、数据起始行、批次大小等配置';

-- 为现有记录设置默认值（可选）
UPDATE gis_manage_template 
SET excel_config = '{"sheetName":"Sheet1","headerRow":1,"dataStartRow":2,"batchSize":1000}'
WHERE template_type = 'excel' AND excel_config IS NULL;

-- 验证列是否添加成功
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'gis_manage_template' 
AND column_name = 'excel_config';
